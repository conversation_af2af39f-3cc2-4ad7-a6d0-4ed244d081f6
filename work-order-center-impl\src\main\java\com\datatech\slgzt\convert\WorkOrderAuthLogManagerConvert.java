package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.WorkOrderAuthLogDO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface WorkOrderAuthLogManagerConvert {

    //do2dto
    WorkOrderAuthLogDTO do2dto(WorkOrderAuthLogDO workOrderAuthLogDO);


    //dto2do
    WorkOrderAuthLogDO dto2do(WorkOrderAuthLogDTO workOrderAuthLogDTO);
}
