package com.datatech.slgzt.model.usercenter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/1/7
 */

@Data
@Accessors(chain = true)
public class UserCenterCreateRoleDTO implements Serializable {

    /**
     * 角色id
     */
    private Long id;

    /**
     * 角色名称
     */
    private String name;

    private String description;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 是否有效 0 无效 1 有效
     */
    private Integer status ;

    /**
     * 0:不是预置值/1:是预置值
     */
    private Integer isPreset = 0;

    private Integer type = 0;
}

