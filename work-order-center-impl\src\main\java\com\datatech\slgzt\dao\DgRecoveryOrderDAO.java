package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.DgRecoveryOrderMapper;
import com.datatech.slgzt.dao.model.DgRecoveryOrderDO;
import com.datatech.slgzt.model.query.DgRecoveryOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 07月07日 14:51:14
 */
@Repository
public class DgRecoveryOrderDAO {
    @Resource
    private DgRecoveryOrderMapper mapper;


    public DgRecoveryOrderDO getById(String id) {
        return mapper.selectById(id);
    }


    public String insert(DgRecoveryOrderDO reconveryWorkOrderDO) {
        mapper.insert(reconveryWorkOrderDO);
        return reconveryWorkOrderDO.getId();
    }


    public void update(DgRecoveryOrderDO orderDO) {
        mapper.updateById(orderDO);
    }


    /**
     * 列表查询
     *
     * @param query 查询条件
     * @return 回收工单列表
     */
    public List<DgRecoveryOrderDO> list(DgRecoveryOrderQuery query) {
        return mapper.selectList(Wrappers.<DgRecoveryOrderDO>lambdaQuery()
                .isNull(Boolean.TRUE.equals(query.getJobExecutionIdNull()), DgRecoveryOrderDO::getJobExecutionId)
                .like(ObjNullUtils.isNotNull(query.getOrderCode()), DgRecoveryOrderDO::getOrderCode, query.getOrderCode())
                .like(ObjNullUtils.isNotNull(query.getCreator()), DgRecoveryOrderDO::getCreator, query.getCreator())
                .le(ObjNullUtils.isNotNull(query.getCreateTimeStart()), DgRecoveryOrderDO::getCreateTime, query.getCreateTimeStart())
                .ge(ObjNullUtils.isNotNull(query.getCreateTimeEnd()), DgRecoveryOrderDO::getCreateTime, query.getCreateTimeEnd())

        );
    }

}
