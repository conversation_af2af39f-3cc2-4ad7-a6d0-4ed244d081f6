package com.datatech.slgzt.model.home;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AuditCountVo {

    /**
     * 待审核数
     */
    private Long pendingCount;

    /**
     * 已审核数
     */
    private Long approvedCount;

    /**
     * 已驳回数
     */
    private Long rejectedCount;



    @Override
    public String toString() {
        return "AuditCountVo{" +
                "pendingCount=" + pendingCount +
                ", approvedCount=" + approvedCount +
                ", rejectedCount=" + rejectedCount +
                '}';
    }
}
