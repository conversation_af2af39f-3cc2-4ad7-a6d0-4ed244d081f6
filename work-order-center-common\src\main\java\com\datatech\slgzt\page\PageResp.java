package com.datatech.slgzt.page;

import lombok.Data;

import java.util.List;

/**
 * 统一分页
 *
 * <AUTHOR>
 * @version v1.0
 */
@Data
public class PageResp<T> {
    private List<T> records;
    private long    size;
    private long    current;
    private long    total;
    private long    pageNumber;

    /**
     * 统一分页构造器
     */
    public PageResp(List<T> records, long current, long size, long total) {
        this.records = records;
        this.current = current;
        this.size = size;
        this.total = total;
        this.pageNumber = this.getPages();
    }

    private long getPages() {
        if (getSize() == 0) {
            return 0L;
        }
        long pages = getTotal() / getSize();
        if (getTotal() % getSize() != 0) {
            pages++;
        }
        return pages;
    }

}