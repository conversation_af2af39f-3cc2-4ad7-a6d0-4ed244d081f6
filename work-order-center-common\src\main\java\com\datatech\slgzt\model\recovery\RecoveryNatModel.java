package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:55:56
 */
@Data
public class RecoveryNatModel extends BaseReconveryProductModel {

    //eipId
    private String natId;

    //网关名称
    @ExcelExportHeader(value = "网关名称")
    private String natName;

    @ExcelExportHeader(value = "VPC")
    private String vpcName;

    //规格名称
    @ExcelExportHeader(value = "实例规格")
    private String spec;

    private Long goodOrderId;

    //eipModel
    private RecoveryEipModel eipModel;

    //网络模型
    private RecoveryPlaneNetworkModel planeNetworkModel;



}
