package com.datatech.slgzt.model.user;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 菜单表 --实体类
 *
 * <AUTHOR>
 * @date 2024/11/18 16:42
 **/
@Data
public class OacMenu {


    private Long id;

    /**
     * 父菜单编号
     */
    private Long parentId;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单类型: menu: 菜单 catalogue: 目录
     */
    private String menuType;

    /**
     * 菜单地址
     */
    private String menuUrl;

    /**
     * 菜单图标
     */
    private String menuIcon;

    /**
     * 菜单描述
     */
    private String menuDesc;

    /**
     * 菜单作用域: tenant: 租户首页 operate: 运营首页 maintenance: 运维首页
     */
    private String menuScope;

    /**
     * 创建人
     */
    private Long createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private Long updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 状态
     */
    private Integer status = 1;

    /**
     * 状态
     */
    private Integer sort;

    /**
     * 所属权限菜单编码
     */
    private String authMenuCode;

    /**
     * 所属角色编码
     */
    private String roleCode;
}