package com.datatech.slgzt.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.ResourceDetailWebConvert;
import com.datatech.slgzt.enums.ChangeTypeResourceDetailStatusEnum;
import com.datatech.slgzt.enums.GoodsTypeEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.enums.ip.ResourceTypeEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.TenantManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.business.req.BusinessQueryRequest;
import com.datatech.slgzt.model.dto.PhysicalMachineImportResultDTO;
import com.datatech.slgzt.model.dto.RegionDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.TenantDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.query.RegionQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.req.resource.ResourceExportReq;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.network.NetworkTableVo;
import com.datatech.slgzt.model.vo.resource.*;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcTableVo;
import com.datatech.slgzt.service.form.OrderFormService;
import com.datatech.slgzt.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月22日 11:51:45
 */
@Slf4j
@RestController
@RequestMapping("/goods")
public class GoodsController {
    //
    //@Value("${file.exportPath}")
    //private String exportPath;
    @Resource
    private ResourceDetailManager resourceDetailManager;


    @Resource
    private OrderFormService orderFormService;

    @Resource
    private BusinessService bussinessService;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private ResourceDetailWebConvert convert;

    @Resource
    private RegionManager regionManager;

    @Resource
    private BusinessService businessService;


    @PostMapping("/resource/export")
    public void exportResourceDetail(@RequestBody ResourceExportReq vo, HttpServletResponse response) {
        // 获取当前用户信息
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser != null, "用户未登录");
        String type = vo.getType();
        Object object = vo.getObject();
        String projectPath = System.getProperty("user.dir");
        if (GoodsTypeEnum.VPC.getCode().equals(type)) {
            VpcTableVo vpcTableVo = JSONObject.parseObject(JSONObject.toJSONString(object), VpcTableVo.class);
            vpcTableVo.setPageSize(99999);
            PageResult<VpcOrderResult> vpcListPageResult = orderFormService.queryVpcListPage(vpcTableVo);
            List<VpcOrderResult> records = vpcListPageResult.getRecords();
            if (CollectionUtil.isNotEmpty(records)) {
                String filePath = projectPath + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
                FileUtils.doExport(records, VpcOrderResult.class, filePath);
                downloadFile(response, filePath, "VPC资源详情.xlsx");
            }
        } else if (GoodsTypeEnum.NETWORK.getCode().equals(type)) {
            NetworkTableVo networkTableVo = JSONObject.parseObject(JSONObject.toJSONString(object), NetworkTableVo.class);
            networkTableVo.setPageSize(99999);
            PageResult<NetworkOrderResult> networkOrderPageResult = orderFormService.queryNetworkListPage(networkTableVo);
            List<NetworkOrderResult> records = networkOrderPageResult.getRecords();
            if (CollectionUtil.isNotEmpty(records)) {
                String filePath = projectPath + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
                FileUtils.doExport(records, NetworkOrderResult.class, filePath);
                downloadFile(response, filePath, "network资源详情.xlsx");
            }
        } else {
            List<UserCenterRoleDTO> oacRoles = currentUser.getOacRoles();
            List<String> roles = StreamUtils.mapArray(oacRoles, UserCenterRoleDTO::getCode);
            ResourceDetailQuery query = JSONObject.parseObject(JSONObject.toJSONString(object), ResourceDetailQuery.class);
            query.setPageSize(99999);
            query.setPageNum(1);
            query.setType(type);
            if (!roles.contains("super_admin") && roles.stream().noneMatch(role -> role.startsWith("operation_group"))) {
                //获取当前用户角色
                List<Long> tenantIds = tenantManager.listTenIdByOwnerId(currentUser.getId());
                if ("DG".equals(query.getSourceType())) {
                    tenantIds = tenantManager.listRelTenantIdByUserId(currentUser.getId());
                }
                if (ObjNullUtils.isNull(tenantIds)) {
                    return;
                }
                query.setTenantList(tenantIds);
            }
            PageResult<ResourceDetailDTO> page = resourceDetailManager.page(query);
            List<ResourceDetailVO> list = StreamUtils.mapArray(page.getRecords(), convert::convertFill);
            //根据类型转成不同的vo对象
            Class clazz = getaClass(type);
            //exportPath 变成项目路径
            // 获取项目根路径
            String filePath = projectPath + "/export/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";
            FileUtils.doExport(list, clazz, filePath);
            downloadFile(response, filePath, GoodsTypeEnum.getByCode(vo.getType()).getDesc() + "资源详情.xlsx");
        }
    }

    private Class getaClass(String type) {
        Class clazz = null;
        if (GoodsTypeEnum.ECS.getCode().equals(type)
                || GoodsTypeEnum.GCS.getCode().equals(type)
                || GoodsTypeEnum.MYSQL.getCode().equals(type)
                || GoodsTypeEnum.REDIS.getCode().equals(type)) {
            clazz = EcsExportVO.class;
        } else if (GoodsTypeEnum.EVS.getCode().equals(type)) {
            clazz = EvsExportVO.class;
        } else if (GoodsTypeEnum.OBS.getCode().equals(type)) {
            clazz = ObsExportVO.class;
        } else if (GoodsTypeEnum.SLB.getCode().equals(type)) {
            clazz = SlbExportVO.class;
        } else if (GoodsTypeEnum.NAT.getCode().equals(type)) {
            clazz = NatExportVO.class;
        } else if (GoodsTypeEnum.EIP.getCode().equals(type)) {
            clazz = EipExportVO.class;
        } else if (GoodsTypeEnum.BACKUP.getCode().equals(type)) {
            clazz = BackupExportVO.class;
        } else if (GoodsTypeEnum.VPN.getCode().equals(type)) {
            clazz = VpnExportVO.class;
        } else if (GoodsTypeEnum.NAS.getCode().equals(type)) {
            clazz = NasExportVO.class;
        } else if (GoodsTypeEnum.FLINK.getCode().equals(type)) {
            clazz = FlinkExportVO.class;
        } else if (GoodsTypeEnum.RDS_MYSQL.getCode().equals(type)) {
            clazz = RdsMysqlExportVO.class;
        } else if (GoodsTypeEnum.PHYSICAL_MACHINE.getCode().equals(type)) {
            clazz = PhysicalMachineExportVO.class;
        } else if (GoodsTypeEnum.KAFKA.getCode().equals(type)) {
            clazz = KafkaExportVO.class;
        } else if (GoodsTypeEnum.ES.getCode().equals(type)) {
            clazz = EsExportVO.class;
        }

        return clazz;
    }

    private void downloadFile(HttpServletResponse response, String exportPath, String fileName) {
        try {
            // 以流的形式下载文件。
            InputStream fis = new BufferedInputStream(Files.newInputStream(Paths.get(exportPath)));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            //清空response
            response.reset();
            //设置response响应头
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 添加Content-Length
            File file = new File(exportPath);
            response.setContentLength((int) file.length());
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(buffer);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            // log.error("文件下载失败：" + e);
        }
    }

    /**
     * 物理机导入模板下载
     *
     * @param response HTTP响应
     */
    @GetMapping("/pm/template/download")
    public void downloadPhysicalMachineTemplate(HttpServletResponse response) {
        try {
            // 生成文件名
            String fileName = "物理机导入模板_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";

            // 创建Excel模板
            String projectPath = System.getProperty("user.dir");
            String filePath = projectPath + "/temp/" + UUID.randomUUID().toString().replaceAll("-", "") + ".xlsx";

            // 确保临时目录存在
            File tempDir = new File(projectPath + "/temp/");
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }

            // 创建Excel模板文件
            createPhysicalMachineTemplate(filePath);

            // 下载文件
            downloadFile(response, filePath, fileName);

            // 删除临时文件
            new File(filePath).delete();

        } catch (Exception e) {
            log.error("物理机模板下载失败", e);
        }
    }

    /**
     * 创建物理机导入Excel模板
     *
     * @param filePath 文件路径
     */
    private void createPhysicalMachineTemplate(String filePath) {
        // 定义表头
        String[] headers = {
                "物理机名称", "交维状态", "系统版本", "显卡类型", "显卡型号", "显卡数量",
                "CPU核心数", "内存(GB)", "硬盘(GB)", "IP地址", "申请时长", "业务系统",
                "资源池", "开通时间", "到期时间", "申请人", "配置项编号"
        };

        // 使用hutool创建Excel
        try (cn.hutool.poi.excel.ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter(filePath)) {
            // 写入表头
            for (int i = 0; i < headers.length; i++) {
                writer.writeCellValue(i, 0, headers[i]);
            }

            // 写入示例数据行 - 区分文本和数值类型
            writeExampleDataRow(writer);
        }
    }

    /**
     * 写入示例数据行，将数值字段设置为Excel数值类型
     *
     * @param writer Excel写入器
     */
    private void writeExampleDataRow(cn.hutool.poi.excel.ExcelWriter writer) {
        // 获取底层的Apache POI对象
        org.apache.poi.ss.usermodel.Sheet sheet = writer.getSheet();
        org.apache.poi.ss.usermodel.Row row = sheet.createRow(1); // 第2行（索引为1）

        // 定义示例数据，按列索引设置
        // 第0列：物理机名称 - 文本
        row.createCell(0).setCellValue("示例物理机名称");

        // 第1列：交维状态 - 文本
        row.createCell(1).setCellValue("已交维");

        // 第2列：系统版本 - 文本
        row.createCell(2).setCellValue("BCLinux for Euler 21.10");

        // 第3列：显卡类型 - 文本
        row.createCell(3).setCellValue("NPU");

        // 第4列：显卡型号 - 文本
        row.createCell(4).setCellValue("910B");

        // 第5列：显卡数量 - 数值类型
        org.apache.poi.ss.usermodel.Cell gpuNumCell = row.createCell(5);
        gpuNumCell.setCellValue(8.0); // 设置为数值类型

        // 第6列：CPU核心数 - 数值类型
        org.apache.poi.ss.usermodel.Cell cpuCell = row.createCell(6);
        cpuCell.setCellValue(192.0); // 设置为数值类型

        // 第7列：内存(GB) - 数值类型
        org.apache.poi.ss.usermodel.Cell memoryCell = row.createCell(7);
        memoryCell.setCellValue(2048.0); // 设置为数值类型

        // 第8列：硬盘(GB) - 数值类型
        org.apache.poi.ss.usermodel.Cell diskCell = row.createCell(8);
        diskCell.setCellValue(96960.0); // 设置为数值类型

        // 第9列：IP地址 - 文本
        row.createCell(9).setCellValue("**************");

        // 第10列：申请时长 - 文本
        row.createCell(10).setCellValue("两年");

        // 第11列：业务系统 - 文本
        row.createCell(11).setCellValue("网络运维AI+Paas平台");

//        // 第12列：所属云 - 文本
//        row.createCell(12).setCellValue("平台云");

        // 第12列：资源池 - 文本
        row.createCell(12).setCellValue("平台云-萧山02");

        // 第13列：开通时间 - 文本
        row.createCell(13).setCellValue("2024-6-21");

        // 第14列：到期时间 - 文本
        row.createCell(14).setCellValue("2026-6-21");

        // 第15列：申请人 - 文本
        row.createCell(15).setCellValue("示例申请人");

        // 第16列：配置项编号 - 文本
        row.createCell(16).setCellValue("唯一id");
    }

    /**
     * 物理机数据导入
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/pm/import")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<PhysicalMachineImportResultDTO> importPhysicalMachineData(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件
            if (file == null || file.isEmpty()) {
                return CommonResult.failure("文件不能为空");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return CommonResult.failure("文件格式不正确，只支持Excel文件(.xlsx, .xls)");
            }

            // 文件大小限制：10MB
            if (file.getSize() > 10 * 1024 * 1024) {
                return CommonResult.failure("文件大小不能超过10MB");
            }

            // 解析Excel文件
            PhysicalMachineImportResultDTO result = parseAndImportExcel(file);

            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("物理机数据导入失败", e);
            return CommonResult.failure("导入失败：" + e.getMessage());
        }
    }

    /**
     * 解析并导入Excel数据
     *
     * @param file Excel文件
     * @return 导入结果
     */
    private PhysicalMachineImportResultDTO parseAndImportExcel(MultipartFile file) throws Exception {
        PhysicalMachineImportResultDTO result = new PhysicalMachineImportResultDTO();
        result.setErrorDetails(new ArrayList<>());

        // 使用hutool解析Excel
        try (cn.hutool.poi.excel.ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream())) {

            // 获取所有行数据（跳过表头）
            List<List<Object>> rows = reader.read(1); // 从第2行开始读取

            if (rows.isEmpty()) {
                result.setTotalRows(0);
                result.setSuccessCount(0);
                result.setFailureCount(0);
                return result;
            }

            // 行数限制：1000行
            if (rows.size() > 1000) {
                throw new RuntimeException("导入数据不能超过1000行");
            }

            result.setTotalRows(rows.size());

            // 获取基础数据用于验证
            List<String> existingDeviceIds = getExistingDeviceIds();
            // 获取所有的业务系统
            Map<String, CmpAppDTO> businessSystemMap = getBusinessSystems();
            Map<String, RegionDTO> resourcePoolMap = getResourcePools();

            List<PhysicalMachineImportVO> validData = new ArrayList<>();

            // 逐行解析和验证
            for (int i = 0; i < rows.size(); i++) {
                int rowNumber = i + 2; // Excel行号（从2开始）
                List<Object> row = rows.get(i);

                try {
                    // 解析行数据
                    PhysicalMachineImportVO vo = parseRowData(row, rowNumber);

                    // 验证数据
                    List<PhysicalMachineImportValidator.ValidationError> errors =
                            PhysicalMachineImportValidator.validateRow(vo, rowNumber, existingDeviceIds, businessSystemMap, resourcePoolMap);

                    if (errors.isEmpty()) {
                        validData.add(vo);
                        // 添加到已存在的设备ID列表中，避免重复
                        if (vo.getDeviceId() != null) {
                            existingDeviceIds.add(vo.getDeviceId());
                        }
                    } else {
                        // 添加错误信息
                        for (PhysicalMachineImportValidator.ValidationError error : errors) {
                            PhysicalMachineImportResultDTO.ImportErrorDetail errorDetail = new PhysicalMachineImportResultDTO.ImportErrorDetail();
                            errorDetail.setRowNumber(error.getRowNumber());
                            errorDetail.setFieldName(error.getFieldName());
                            errorDetail.setErrorMessage(error.getErrorMessage());
                            errorDetail.setErrorValue(error.getErrorValue());
                            result.getErrorDetails().add(errorDetail);
                        }
                    }

                } catch (Exception e) {
                    // 行解析异常
                    PhysicalMachineImportResultDTO.ImportErrorDetail errorDetail = new PhysicalMachineImportResultDTO.ImportErrorDetail();
                    errorDetail.setRowNumber(rowNumber);
                    errorDetail.setFieldName("整行数据");
                    errorDetail.setErrorMessage("行数据解析失败：" + e.getMessage());
                    errorDetail.setErrorValue("");
                    result.getErrorDetails().add(errorDetail);
                }
            }

            result.setSuccessCount(validData.size());
            result.setFailureCount(result.getTotalRows() - result.getSuccessCount());
            // 如果有错误数据，则不导入
            if (result.getFailureCount() > 0) {
                return result;
            } else {
                batchInsertPhysicalMachines(validData, businessSystemMap, resourcePoolMap);
            }
        }
        return result;
    }

    /**
     * 解析Excel行数据为VO对象
     *
     * @param row Excel行数据
     * @param rowNumber 行号
     * @return PhysicalMachineImportVO对象
     */
    private PhysicalMachineImportVO parseRowData(List<Object> row, int rowNumber) {
        PhysicalMachineImportVO vo = new PhysicalMachineImportVO();

        // 确保有足够的列数据
        if (row.size() < 17) {
            throw new RuntimeException("第" + rowNumber + "行数据列数不足，应为17列");
        }

        vo.setDeviceName(getCellValue(row, 0));
        vo.setHandoverStatus(getCellValue(row, 1));
        vo.setOsVersion(getCellValue(row, 2));
        vo.setGpuCardType(getCellValue(row, 3));
        vo.setGpuType(getCellValue(row, 4));
        vo.setGpuNum(getCellValue(row, 5));
        vo.setCpu(getCellValue(row, 6));
        vo.setMemory(getCellValue(row, 7));
        vo.setDataDisk(getCellValue(row, 8));
        vo.setIp(getCellValue(row, 9));
        vo.setApplyTime(getCellValue(row, 10));
        vo.setBusinessSysName(getCellValue(row, 11));
        vo.setResourcePoolName(getCellValue(row, 12));
        vo.setResourceApplyTime(getCellValue(row, 13));
        vo.setExpireTime(getCellValue(row, 14));
        vo.setApplyUserName(getCellValue(row, 15));
        vo.setDeviceId(getCellValue(row, 16));

        return vo;
    }

    /**
     * 获取单元格值并转换为字符串
     *
     * @param row 行数据
     * @param index 列索引
     * @return 单元格值的字符串表示
     */
    private String getCellValue(List<Object> row, int index) {
        if (index >= row.size() || row.get(index) == null) {
            return null;
        }
        return row.get(index).toString().trim();
    }

    /**
     * 获取已存在的设备ID列表
     *
     * @return 设备ID列表
     */
    private List<String> getExistingDeviceIds() {
        try {
            // 查询所有物理机类型的资源详情
            ResourceDetailQuery query = new ResourceDetailQuery();
            query.setType(ResourceTypeEnum.PHYSICAL_MACHINE.getAbbreviate());
            query.setSourceType("XX");
            query.setPageNum(1);
            query.setPageSize(1000); // 获取所有数据

            PageResult<ResourceDetailDTO> pageResult = resourceDetailManager.page(query);
            return pageResult.getRecords().stream()
                    .map(ResourceDetailDTO::getDeviceId)
                    .filter(Objects::nonNull)
                    .collect(java.util.stream.Collectors.toList());
        } catch (Exception e) {
            log.warn("获取已存在设备ID列表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有的业务系统
     * @return 业务系统名称-实例map
     */
    private Map<String, CmpAppDTO> getBusinessSystems() {
        BusinessQueryRequest queryRequest = new BusinessQueryRequest();
        List<CmpAppDTO> businessSystems = businessService.listNomal(queryRequest);
        return StreamUtils.toMap(businessSystems, CmpAppDTO::getSystemName);
    }

    /**
     * 获取所有的资源池
     * @return 资源池名称-实例map
     */
    private Map<String, RegionDTO> getResourcePools() {
        // 通过RegionManager查询有效的资源池名称
        RegionQuery query = new RegionQuery();
        List<RegionDTO> regions = regionManager.list(query);
        return StreamUtils.toMap(regions, RegionDTO::getName);
    }

    /**
     * 批量插入物理机数据到数据库
     *
     * @param validData 有效的物理机数据列表
     */
    private void batchInsertPhysicalMachines(List<PhysicalMachineImportVO> validData,
                                             Map<String, CmpAppDTO> businessSystemMap,
                                             Map<String, RegionDTO> resourcePoolMap) {
        log.info("批量插入物理机数据，数量：{}", validData.size());

        List<ResourceDetailDTO> resourceDetailList = new ArrayList<>();

        for (PhysicalMachineImportVO vo : validData) {
            ResourceDetailDTO dto = new ResourceDetailDTO();

            // 设置固定字段
            // ID由MyBatis Plus自动生成，不需要手动设置
            dto.setType(ResourceTypeEnum.PHYSICAL_MACHINE.getAbbreviate());
            dto.setCreateTime(LocalDateTime.now());
            dto.setStatus(1); // 正常状态

            // 设置基本信息
            dto.setDeviceName(vo.getDeviceName());
            dto.setDeviceId(vo.getDeviceId());
            // 之前提供的数据，配置项是cmdb id
            dto.setConfigId(vo.getDeviceId());
            dto.setHandoverStatus(vo.getHandoverStatus());
            dto.setOsVersion(vo.getOsVersion());
            dto.setGpuCardType(vo.getGpuCardType());
            dto.setGpuType(vo.getGpuType());

            // 处理数量字段
            if (vo.getGpuNum() != null) {
                try {
                    dto.setGpuNum(Integer.parseInt(vo.getGpuNum()));
                } catch (NumberFormatException e) {
                    log.warn("GPU数量解析失败: {}", vo.getGpuNum());
                }
            }

            // 组合CPU和内存为SPEC字段
            dto.setSpec(PhysicalMachineImportValidator.combineSpec(vo.getCpu(), vo.getMemory()));

            // 格式化硬盘容量，自动添加GB单位
            dto.setDataDisk(PhysicalMachineImportValidator.formatDataDisk(vo.getDataDisk()));
            dto.setIp(vo.getIp());
            dto.setApplyTime(PhysicalMachineImportValidator.formatApplyTime(vo.getApplyTime()));
            dto.setBusinessSysName(vo.getBusinessSysName());
            dto.setResourcePoolName(vo.getResourcePoolName());
            dto.setApplyUserName(vo.getApplyUserName());

            // 解析日期字段
            dto.setResourceApplyTime(PhysicalMachineImportValidator.parseDate(vo.getResourceApplyTime()));
            // 生效时间和resourceApplyTime 一致，分页查询的时候，使用的是它
            dto.setEffectiveTime(PhysicalMachineImportValidator.parseDate(vo.getResourceApplyTime()));
            dto.setExpireTime(PhysicalMachineImportValidator.parseDate(vo.getExpireTime()));

            // 查询并设置业务系统ID和资源池ID
            if (vo.getBusinessSysName() != null) {
                if (businessSystemMap.containsKey(vo.getBusinessSysName())) {
                    dto.setBusinessSysId(businessSystemMap.get(vo.getBusinessSysName()).getSystemId());
                } else {
                    log.warn("找不到对应的业务系统: {}", vo.getBusinessSysName());
                }
            }

            if (vo.getResourcePoolName() != null) {
                if (!resourcePoolMap.containsKey(vo.getResourcePoolName())) {
                    log.warn("找不到对应的资源池: {}", vo.getResourcePoolName());
                    continue;
                }
                RegionDTO regionDTO = resourcePoolMap.get(vo.getResourcePoolName());
                dto.setResourcePoolId(String.valueOf(regionDTO.getId()));
                dto.setResourcePoolCode(regionDTO.getCode());
                dto.setDomainCode(regionDTO.getDomainCode());
                dto.setDomainName(Objects.requireNonNull(CatalogueDomain.getByCode(regionDTO.getDomainCode())).getName());
                dto.setCloudPlatform(Objects.requireNonNull(CatalogueDomain.getByCode(regionDTO.getDomainCode())).getName());
            }

            // 默认值
            dto.setSourceType("XX");
            dto.setChangeStatus(ChangeTypeResourceDetailStatusEnum.UN_CHANGE.getType());
            dto.setRecoveryStatus(0);
            dto.setGoodsOrderId(IdUtil.getSnowflake().nextId());

            CmpAppDTO cmpAppDTO = businessService.getById(dto.getBusinessSysId());
            if (cmpAppDTO != null) {
                dto.setTenantId(cmpAppDTO.getTenantId());
                TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
                if (tenantDTO != null) {
                    dto.setTenantName(tenantDTO.getName());
                }
            }

            resourceDetailList.add(dto);
        }

        // 批量插入数据库
        if (!resourceDetailList.isEmpty()) {
            resourceDetailManager.batchSaveResourceDetail(resourceDetailList);
            log.info("成功批量插入物理机数据，数量：{}", resourceDetailList.size());
        }
    }
}
