package com.datatech.slgzt.utils;

import java.util.HashMap;
import java.util.Map;

public class HeadersUtil {
    private final static Map<String, String> header;

    public HeadersUtil() {
    }
    static {
        header = new HashMap<>();
        header.put("RemoteUser", "BusinessCenter");
        header.put("Content-Type", "application/json");
    }
    public static Map<String, String> getHeaders() {
        return header;
    }
}
