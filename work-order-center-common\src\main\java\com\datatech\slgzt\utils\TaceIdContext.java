package com.datatech.slgzt.utils;

import cn.hutool.core.util.IdUtil;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月17日 14:19:26
 */
public class TaceIdContext {
    private static final ThreadLocal<String> TRACE_ID = new ThreadLocal<>();

    //生成traceId
    public static String generateTraceIdGet() {
        //如果当前线程已经有traceId则直接返回
        if (TRACE_ID.get() != null) {
            return TRACE_ID.get();
        }
        String id = IdUtil.nanoId();
        TRACE_ID.set(id);
        return id;
    }

    public static void removeTraceId() {
        TRACE_ID.remove();
    }


}
