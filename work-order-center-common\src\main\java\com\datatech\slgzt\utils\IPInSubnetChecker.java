package com.datatech.slgzt.utils;


import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * nat网络规则判断类
 * <AUTHOR>
 */
@Slf4j
public class IPInSubnetChecker {

    /**
     * 判断一个IP地址是否在指定的网段内
     *
     * @param ipAddress 输入的IP地址，例如 "***********"
     * @param subnet    网段，例如 "***********/24"
     * @return 如果IP在网段内，返回true；否则返回false
     * @throws UnknownHostException 如果IP地址格式不正确
     */
    public static boolean isIpInSubnet(String ipAddress, String subnet) {
        // 分割网段，获取网络地址和掩码位数
        String[] parts = subnet.split("/");
        if (parts.length != 2) {
            return false;
        }
        try {
            String subnetIp = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            // 将IP地址转换为整数形式
            long ip = ipToLong(InetAddress.getByName(ipAddress));
            long subnetLong = ipToLong(InetAddress.getByName(subnetIp));
            // 生成掩码
            long mask = prefixLength == 0 ? 0 : ~((1L << (32 - prefixLength)) - 1) & 0xFFFFFFFFL;
            // 比较网络地址
            return (ip & mask) == (subnetLong & mask);
        } catch (UnknownHostException e) {
            return false;
        }

    }

    /**
     * 将InetAddress转换为长整型数值
     *
     * @param inetAddr 要转换的InetAddress
     * @return 转换后的长整型数值
     */
    private static long ipToLong(InetAddress inetAddr) {
        byte[] octets = inetAddr.getAddress();
        long result = 0;
        for (byte octet : octets) {
            result = (result << 8) | (octet & 0xFF);
        }
        return result;
    }


    public static boolean isSubnetOf(String childCidr, String parentCidr) {
        String[] childParts = childCidr.split("/");
        String[] parentParts = parentCidr.split("/");

        try {
            InetAddress childAddress = InetAddress.getByName(childParts[0]);
            InetAddress parentAddress = InetAddress.getByName(parentParts[0]);

            int childPrefix = Integer.parseInt(childParts[1]);
            int parentPrefix = Integer.parseInt(parentParts[1]);

            // 父网段必须比子网段“更大”或“相等”
            if (parentPrefix > childPrefix) return false;

            byte[] childBytes = childAddress.getAddress();
            byte[] parentBytes = parentAddress.getAddress();

            // 计算父网段的掩码
            int maskBits = parentPrefix;
            byte[] mask = new byte[4];
            for (int i = 0; i < 4; i++) {
                if (maskBits >= 8) {
                    mask[i] = (byte) 0xFF;
                    maskBits -= 8;
                } else if (maskBits > 0) {
                    mask[i] = (byte) (0xFF << (8 - maskBits));
                    maskBits = 0;
                } else {
                    mask[i] = 0;
                }
            }

            // 用掩码分别处理两个地址
            for (int i = 0; i < 4; i++) {
                if ((childBytes[i] & mask[i]) != (parentBytes[i] & mask[i])) {
                    return false;
                }
            }
            return true;
        }catch (Exception e){
            return false;
        }
    }


    // 授权IP最大数量
    private static final int MAX_IPS = 10;

    // 正则匹配端口格式（单个端口或端口区间）
    private static final String PORT_REGEX = "^(\\d{1,5})(-(\\d{1,5}))?$";
    // 正则匹配IPv4地址或CIDR
    private static final String IP_CIDR_REGEX =
            "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(/(3[0-2]|[12][0-9]|[0-9]))?$";

    /**
     * 校验端口
     */
    public static boolean validatePortRange(String portStr) {
        if (portStr == null || portStr.trim().isEmpty()) return false;
        String[] ports = portStr.split(",");

        for (String p : ports) {
            Matcher m = Pattern.compile(PORT_REGEX).matcher(p.trim());
            if (!m.matches()) {
                log.warn("Invalid port format: {}" ,p);
                return false;
            }

            int start = Integer.parseInt(m.group(1));
            int end = m.group(3) != null ? Integer.parseInt(m.group(3)) : start;

            if (start < 0 || end > 65535 || start > end) {
                log.warn("Port out of range: {}", p);
                return false;
            }
        }

        return true;
    }

    /**
     * 校验ip
     */
    public static boolean validateIpList(String ipStr) {
        if (ipStr == null || ipStr.trim().isEmpty()) return false;
        String[] ips = ipStr.split(",");
        Set<String> uniqueIps = new HashSet<>();

        if (ips.length > MAX_IPS) {
            log.warn("Too many IPs/CIDRs: max is {}", MAX_IPS);
            return false;
        }

        for (String ip : ips) {
            String trimmed = ip.trim();
            if (!Pattern.matches(IP_CIDR_REGEX, trimmed)) {
                log.warn("Invalid IP or CIDR: {}", trimmed);
                return false;
            }

            if (!uniqueIps.add(trimmed)) {
                log.warn("Duplicate IP/CIDR: {}", trimmed);
                return false;
            }
        }

        return true;
    }

    // 示例用法
    public static void main(String[] args) {

//
//            String bigCidr = "10.0.0.0";
//            String smallCidr = "10.0.0.0/30";
//        System.out.println(bigCidr.contains("."));

        String portInput = "8080,90,1010,8080/9090";
        String ipInput = "***********-24,********,********,********";

//        System.out.println("Validating port input: " + portInput);
        System.out.println("Result: " + validatePortRange(portInput));

//        System.out.println("Validating IP input: " + ipInput);
        System.out.println("Result: " + validateIpList(ipInput));
    }
}

