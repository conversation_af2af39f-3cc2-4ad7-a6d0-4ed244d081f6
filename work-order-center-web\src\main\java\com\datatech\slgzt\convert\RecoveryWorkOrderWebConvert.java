package com.datatech.slgzt.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.ApprovalTimeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.recovery.RecoveryWorkOrderDetailDTO;
import com.datatech.slgzt.model.opm.RecoveryWorkOrderCreateOpm;
import com.datatech.slgzt.model.query.RecoveryWorkOrderQuery;
import com.datatech.slgzt.model.recovery.*;
import com.datatech.slgzt.model.req.recovery.RecoveryWorkOrderCreateReq;
import com.datatech.slgzt.model.req.recovery.RecoveryWorkOrderPageReq;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.recovery.RecoveryWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.recovery.export.*;
import com.datatech.slgzt.model.vo.standard.RecoveryWorkOrderVO;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.ArrayListMultimap;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface RecoveryWorkOrderWebConvert {


    RecoveryWorkOrderCreateOpm convert(RecoveryWorkOrderCreateReq req);

    RecoveryWorkOrderQuery pageReq2Query(RecoveryWorkOrderPageReq req);

    RecoveryWorkOrderVO dto2vo(RecoveryWorkOrderDTO orderDTO);

    RecoveryWorkOrderDetailDTO vo2dto(RecoveryWorkOrderDetailVO vo);

    RecoveryWorkOrderDetailVO dto2voDetail(RecoveryWorkOrderDetailDTO detailDTO);

    @Mapping(target = "orderType", defaultValue = "orderType", qualifiedByName = "orderType")
    RecoveryWorkOrderDetailVO convertDetail(RecoveryWorkOrderDTO dto);

    @Named("orderType")
    default String orderType(String orderType) {
        return "资源回收";
    }


    default void fillProductDetail(RecoveryWorkOrderDetailVO vo,
                                   List<ResourceDetailDTO> resourceDetailDTOS,
                                   List<VpcOrderResult> vpcOrderResultList,
                                   List<NetworkOrderResult> networkOrderResultList,
                                   ArrayListMultimap<String, RecoveryWorkOrderProductDTO> type2product) {
        //resourceDetailDTOS 变成 ArrayListMultimap 用来填充对象
        Map<Long, ResourceDetailDTO> map = StreamUtils.toMap(resourceDetailDTOS, ResourceDetailDTO::getId);

        //设置工单部分信息
        //-------------------------ecs-------------------------------------------
        List<RecoveryEcsModel> ecsListExt = Lists.newArrayList();
        type2product.get("ecs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryEcsModel recoveryEcsModel = JSON.parseObject(propertySnapshot, RecoveryEcsModel.class);
            recoveryEcsModel.setSyncRecovery(productDTO.getSyncRecovery());
            ResourceDetailDTO resourceDetailDTO = map.get(Long.valueOf(productDTO.getResourceDetailId()));
            if (resourceDetailDTO != null) {
                resourceDetailDTO.setSyncRecovery(productDTO.getSyncRecovery());
            }
            commonFill(productDTO, recoveryEcsModel);
            ecsListExt.add(recoveryEcsModel);
        });
        vo.setEcsListExt(ecsListExt);
        //-------------------------gcs-------------------------------------------
        List<RecoveryEcsModel> gcsListExt = Lists.newArrayList();
        type2product.get("gcs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryEcsModel recoveryEcsModel = JSON.parseObject(propertySnapshot, RecoveryEcsModel.class);
            recoveryEcsModel.setSyncRecovery(productDTO.getSyncRecovery());
            ResourceDetailDTO resourceDetailDTO = map.get(Long.valueOf(productDTO.getResourceDetailId()));
            if (resourceDetailDTO != null) {
                resourceDetailDTO.setSyncRecovery(productDTO.getSyncRecovery());
            }
            commonFill(productDTO, recoveryEcsModel);
            gcsListExt.add(recoveryEcsModel);
        });
        vo.setGcsListExt(gcsListExt);
        //-------------------------mysql-------------------------------------------
        List<RecoveryEcsModel> mysqlListExt = Lists.newArrayList();
        type2product.get("mysql").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryEcsModel recoveryEcsModel = JSON.parseObject(propertySnapshot, RecoveryEcsModel.class);
            recoveryEcsModel.setSyncRecovery(productDTO.getSyncRecovery());
            ResourceDetailDTO resourceDetailDTO = map.get(Long.valueOf(productDTO.getResourceDetailId()));
            if (resourceDetailDTO != null) {
                resourceDetailDTO.setSyncRecovery(productDTO.getSyncRecovery());
            }
            commonFill(productDTO, recoveryEcsModel);
            mysqlListExt.add(recoveryEcsModel);
        });
        vo.setMysqlListExt(mysqlListExt);
        //-------------------------redis-------------------------------------------
        List<RecoveryEcsModel> redisListExt = Lists.newArrayList();
        type2product.get("redis").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryEcsModel recoveryEcsModel = JSON.parseObject(propertySnapshot, RecoveryEcsModel.class);
            recoveryEcsModel.setSyncRecovery(productDTO.getSyncRecovery());
            ResourceDetailDTO resourceDetailDTO = map.get(Long.valueOf(productDTO.getResourceDetailId()));
            if (resourceDetailDTO != null) {
                resourceDetailDTO.setSyncRecovery(productDTO.getSyncRecovery());
            }
            commonFill(productDTO, recoveryEcsModel);
            redisListExt.add(recoveryEcsModel);
        });
        vo.setRedisListExt(redisListExt);
        //-------------------------evs-------------------------------------------
        List<RecoveryEvsModel> evsListExt = Lists.newArrayList();
        type2product.get("evs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryEvsModel recoveryEvsModel = JSON.parseObject(propertySnapshot, RecoveryEvsModel.class);
            commonFill(productDTO, recoveryEvsModel);
            evsListExt.add(recoveryEvsModel);
        });
        vo.setEvsListExt(evsListExt);
        //-------------------------eip-------------------------------------------
        List<RecoveryEipModel> eipListExt = Lists.newArrayList();
        type2product.get("eip").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryEipModel recoveryEipModel = JSON.parseObject(propertySnapshot, RecoveryEipModel.class);
            commonFill(productDTO, recoveryEipModel);
            recoveryEipModel.setRelatedDeviceType(ProductTypeEnum.getByCode(recoveryEipModel.getRelatedDeviceType()).getDesc());
            eipListExt.add(recoveryEipModel);
        });
        vo.setEipListExt(eipListExt);
        //-------------------------slb-------------------------------------------
        List<RecoverySlbModel> slbListExt = Lists.newArrayList();
        type2product.get("slb").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoverySlbModel recoverySlbModel = JSON.parseObject(propertySnapshot, RecoverySlbModel.class);
            ResourceDetailDTO resourceDetailDTO = map.get(Long.valueOf(productDTO.getResourceDetailId()));
            if (resourceDetailDTO != null) {
                resourceDetailDTO.setSyncRecovery(productDTO.getSyncRecovery());
            }
            recoverySlbModel.setSyncRecovery(productDTO.getSyncRecovery());
            commonFill(productDTO, recoverySlbModel);
            slbListExt.add(recoverySlbModel);
        });
        vo.setSlbListExt(slbListExt);
        //-------------------------obs-------------------------------------------
        List<RecoveryObsModel> obsListExt = Lists.newArrayList();
        type2product.get("obs").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryObsModel recoveryObsModel = JSON.parseObject(propertySnapshot, RecoveryObsModel.class);
            commonFill(productDTO, recoveryObsModel);
            obsListExt.add(recoveryObsModel);
        });
        vo.setObsListExt(obsListExt);
        //-------------------------nat-------------------------------------------
        List<RecoveryNatModel> natListExt = Lists.newArrayList();
        type2product.get("nat").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryNatModel recoveryNatModel = JSON.parseObject(propertySnapshot, RecoveryNatModel.class);
            ResourceDetailDTO resourceDetailDTO = map.get(Long.valueOf(productDTO.getResourceDetailId()));
            if (resourceDetailDTO != null) {
                resourceDetailDTO.setSyncRecovery(productDTO.getSyncRecovery());
            }
            recoveryNatModel.setSyncRecovery(productDTO.getSyncRecovery());
            commonFill(productDTO, recoveryNatModel);
            natListExt.add(recoveryNatModel);
        });
        vo.setNatListExt(natListExt);
        //-------------------------vpc-------------------------------------------
        List<RecoveryVpcModel> vpcListExt = Lists.newArrayList();
        type2product.get("vpc").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryVpcModel recoveryVpcModel = JSON.parseObject(propertySnapshot, RecoveryVpcModel.class);
            commonFill(productDTO, recoveryVpcModel);
            vpcListExt.add(recoveryVpcModel);
        });
        vo.setVpcListExt(vpcListExt);
        //-------------------------network-------------------------------------------
        List<RecoveryNetworkModel> networkListExt = Lists.newArrayList();
        type2product.get("network").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryNetworkModel recoveryNetworkModel = JSON.parseObject(propertySnapshot, RecoveryNetworkModel.class);
            commonFill(productDTO, recoveryNetworkModel);
            networkListExt.add(recoveryNetworkModel);
        });
        vo.setNetworkListExt(networkListExt);
        //-------------------------backup-------------------------------------------
        List<RecoveryBackupModel> backupListExt = Lists.newArrayList();
        type2product.get("backup").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryBackupModel recoveryBackupModel = JSON.parseObject(propertySnapshot, RecoveryBackupModel.class);
            commonFill(productDTO, recoveryBackupModel);
            backupListExt.add(recoveryBackupModel);
        });
        vo.setBackupListExt(backupListExt);
        //-------------------------vpn-------------------------------------------
        List<RecoveryVpnModel> vpnListExt = Lists.newArrayList();
        type2product.get("vpn").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryVpnModel recoveryVpnModel = JSON.parseObject(propertySnapshot, RecoveryVpnModel.class);
            commonFill(productDTO, recoveryVpnModel);
            vpnListExt.add(recoveryVpnModel);
        });
        vo.setVpnListExt(vpnListExt);
        //-------------------------nas-------------------------------------------
        List<RecoveryNasModel> nasListExt = Lists.newArrayList();
        type2product.get("nas").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryNasModel recoveryNasModel = JSON.parseObject(propertySnapshot, RecoveryNasModel.class);
            commonFill(productDTO, recoveryNasModel);
            nasListExt.add(recoveryNasModel);
        });
        vo.setNasListExt(nasListExt);
        //-------------------------pm-------------------------------------------
        List<RecoveryPmModel> pmListExt = Lists.newArrayList();
        type2product.get("pm").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryPmModel recoveryPmModel = JSON.parseObject(propertySnapshot, RecoveryPmModel.class);
            commonFill(productDTO, recoveryPmModel);
            pmListExt.add(recoveryPmModel);
        });
        vo.setPmListExt(pmListExt);
        //-------------------------kafka-------------------------------------------
        List<RecoveryKafkaModel> kafkaListExt = Lists.newArrayList();
        type2product.get("kafka").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryKafkaModel recoveryKafkaModel = JSON.parseObject(propertySnapshot, RecoveryKafkaModel.class);
            commonFill(productDTO, recoveryKafkaModel);
            kafkaListExt.add(recoveryKafkaModel);
        });
        vo.setKafkaListExt(kafkaListExt);
        //-------------------------flink-------------------------------------------
        List<RecoveryFlinkModel> flinkListExt = Lists.newArrayList();
        type2product.get("flink").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryFlinkModel recoveryFlinkModel = JSON.parseObject(propertySnapshot, RecoveryFlinkModel.class);
            commonFill(productDTO, recoveryFlinkModel);
            flinkListExt.add(recoveryFlinkModel);
        });
        vo.setFlinkListExt(flinkListExt);
        //-------------------------es-------------------------------------------
        List<RecoveryEsModel> esListExt = Lists.newArrayList();
        type2product.get("es").forEach(productDTO -> {
            String propertySnapshot = productDTO.getPropertySnapshot();
            RecoveryEsModel recoveryEsModel = JSON.parseObject(propertySnapshot, RecoveryEsModel.class);
            commonFill(productDTO, recoveryEsModel);
            esListExt.add(recoveryEsModel);
        });
        vo.setEsListExt(esListExt);

        ArrayListMultimap<String, ResourceDetailDTO> type2ResDeatil = StreamUtils.toArrayListMultimap(resourceDetailDTOS, ResourceDetailDTO::getType);
        vo.setEcsList(type2ResDeatil.get("ecs"));
        vo.setGcsList(type2ResDeatil.get("gcs"));
        vo.setMysqlList(type2ResDeatil.get("mysql"));
        vo.setRedisList(type2ResDeatil.get("redis"));
        vo.setEvsList(type2ResDeatil.get("evs"));
        vo.setEipList(type2ResDeatil.get("eip"));
        vo.setSlbList(type2ResDeatil.get("slb"));
        vo.setObsList(type2ResDeatil.get("obs"));
        vo.setNatList(type2ResDeatil.get("nat"));
        vo.setVpcList(vpcOrderResultList);
        vo.setNetworkList(networkOrderResultList);
        vo.setBackupList(type2ResDeatil.get("backup"));
        vo.setVpnList(type2ResDeatil.get("vpn"));
        vo.setNasList(type2ResDeatil.get("nas"));
        vo.setPmList(type2ResDeatil.get("pm"));
        vo.setKafkaList(type2ResDeatil.get("kafka"));
        vo.setFlinkList(type2ResDeatil.get("flink"));
        vo.setEsList(type2ResDeatil.get("es"));
    }

    default ResourceExport fillResourceExportDetail(List<RecoveryWorkOrderProductDTO> productDTOS, String productType) {
        ResourceExport resourceExport = new ResourceExport();
        switch (productType) {
            case "ecs":
                List<EcsExportVO> ecsListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryEcsModel recoveryEcsModel = JSON.parseObject(propertySnapshot, RecoveryEcsModel.class);
                    commonFill(productDTO, recoveryEcsModel);
                    fillDataDisk(recoveryEcsModel);
                    EcsExportVO ecsExportVO = BeanUtil.copyProperties(recoveryEcsModel, EcsExportVO.class);
                    RecoveryEipModel eipModel = recoveryEcsModel.getEipModel();
                    if (eipModel != null) {
                        ecsExportVO.setEip(eipModel.getEip());
                        ecsExportVO.setBandwidth(eipModel.getBandwidth());
                    }
                    ecsListExt.add(ecsExportVO);
                });
                resourceExport.setEcsExportVOS(ecsListExt);
                break;
            case "gcs":
                List<EcsExportVO> gcsListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryEcsModel recoveryEcsModel = JSON.parseObject(propertySnapshot, RecoveryEcsModel.class);
                    commonFill(productDTO, recoveryEcsModel);
                    fillDataDisk(recoveryEcsModel);
                    RecoveryEipModel eipModel = recoveryEcsModel.getEipModel();
                    EcsExportVO ecsExportVO = BeanUtil.copyProperties(recoveryEcsModel, EcsExportVO.class);
                    if (eipModel != null) {
                        ecsExportVO.setEip(eipModel.getEip());
                        ecsExportVO.setBandwidth(eipModel.getBandwidth());
                    }
                    gcsListExt.add(ecsExportVO);
                });
                resourceExport.setGcsExportVOS(gcsListExt);
                break;
            case "evs":
                List<EvsExportVO> evsListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryEvsModel recoveryEvsModel = JSON.parseObject(propertySnapshot, RecoveryEvsModel.class);
                    commonFill(productDTO, recoveryEvsModel);
                    EvsExportVO evsExportVO = BeanUtil.copyProperties(recoveryEvsModel, EvsExportVO.class);
                    Boolean mount = Optional.ofNullable(recoveryEvsModel.getMountVm()).orElse(false);
                    evsExportVO.setMountVm(mount ? "是" : "否");
                    evsListExt.add(evsExportVO);
                });
                resourceExport.setEvsExportVOS(evsListExt);
                break;
            case "eip":
                List<EipExportVO> eipListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryEipModel recoveryEipModel = JSON.parseObject(propertySnapshot, RecoveryEipModel.class);
                    commonFill(productDTO, recoveryEipModel);
                    EipExportVO eipExportVO = BeanUtil.copyProperties(recoveryEipModel, EipExportVO.class);
                    eipListExt.add(eipExportVO);
                });
                resourceExport.setEipExportVOS(eipListExt);
                break;
            case "slb":
                List<SlbExportVO> slbListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoverySlbModel recoverySlbModel = JSON.parseObject(propertySnapshot, RecoverySlbModel.class);
                    commonFill(productDTO, recoverySlbModel);
                    SlbExportVO slbExportVO = BeanUtil.copyProperties(recoverySlbModel, SlbExportVO.class);
                    slbListExt.add(slbExportVO);
                });
                resourceExport.setSlbExportVOS(slbListExt);
                break;
            case "obs":
                List<ObsExportVO> obsListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryObsModel recoveryObsModel = JSON.parseObject(propertySnapshot, RecoveryObsModel.class);
                    commonFill(productDTO, recoveryObsModel);
                    ObsExportVO obsExportVO = BeanUtil.copyProperties(recoveryObsModel, ObsExportVO.class);
                    obsListExt.add(obsExportVO);
                });
                resourceExport.setObsExportVOS(obsListExt);
                break;
            case "nat":
                List<NatExportVO> natListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryNatModel recoveryNatModel = JSON.parseObject(propertySnapshot, RecoveryNatModel.class);
                    commonFill(productDTO, recoveryNatModel);
                    NatExportVO natExportVO = BeanUtil.copyProperties(recoveryNatModel, NatExportVO.class);
                    RecoveryEipModel eipModel = recoveryNatModel.getEipModel();
                    if (eipModel != null) {
                        natExportVO.setEip(eipModel.getEip());
                        natExportVO.setBandwidth(eipModel.getBandwidth());
                    }
                    RecoveryPlaneNetworkModel planeNetworkModel = recoveryNatModel.getPlaneNetworkModel();
                    if (planeNetworkModel != null) {
                        List<RecoveryPlaneNetworkModel.Subnet> subnets = planeNetworkModel.getSubnets();
                        String subnetNames = subnets.stream().map(RecoveryPlaneNetworkModel.Subnet::getSubnetName).collect(Collectors.joining(","));
                        natExportVO.setSubnetName(subnetNames);
                    }
                    natListExt.add(natExportVO);
                });
                resourceExport.setNatExportVOS(natListExt);
                break;
            case "vpc":
                List<VpcExportVO> vpcListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryVpcModel recoveryVpcModel = JSON.parseObject(propertySnapshot, RecoveryVpcModel.class);
                    commonFill(productDTO, recoveryVpcModel);
                    VpcExportVO vpcExportVO = BeanUtil.copyProperties(recoveryVpcModel, VpcExportVO.class);
                    vpcListExt.add(vpcExportVO);
                });
                resourceExport.setVpcExportVOS(vpcListExt);
                break;
            case "network":
                List<NetworkExportVO> networkListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryNetworkModel recoveryNetworkModel = JSON.parseObject(propertySnapshot, RecoveryNetworkModel.class);
                    commonFill(productDTO, recoveryNetworkModel);
                    NetworkExportVO networkExportVO = BeanUtil.copyProperties(recoveryNetworkModel, NetworkExportVO.class);
                    networkListExt.add(networkExportVO);
                });
                resourceExport.setNetworkExportVOS(networkListExt);
                break;
            case "backup":
                List<BackupExportVO> backupListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryBackupModel recoveryBackupModel = JSON.parseObject(propertySnapshot, RecoveryBackupModel.class);
                    commonFill(productDTO, recoveryBackupModel);
                    fillBackup(recoveryBackupModel);
                    BackupExportVO backupExportVO = BeanUtil.copyProperties(recoveryBackupModel, BackupExportVO.class);
                    backupListExt.add(backupExportVO);
                });
                resourceExport.setBackupExportVOS(backupListExt);
                break;
            case "vpn":
                List<VpnExportVO> vpnListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryVpnModel recoveryVpnModel = JSON.parseObject(propertySnapshot, RecoveryVpnModel.class);
                    commonFill(productDTO, recoveryVpnModel);
                    //fillBackup(recoveryBackupModel);
                    VpnExportVO vpnExportVO = BeanUtil.copyProperties(recoveryVpnModel, VpnExportVO.class);
                    vpnExportVO.setBandwidth(Objects.isNull(recoveryVpnModel.getBandwidth()) ? "" : recoveryVpnModel.getBandwidth() + "M");
                    vpnListExt.add(vpnExportVO);
                });
                resourceExport.setVpnExportVOS(vpnListExt);
                break;
            case "nas":
                List<NasExportVO> nasListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryNasModel recoveryNasModel = JSON.parseObject(propertySnapshot, RecoveryNasModel.class);
                    commonFill(productDTO, recoveryNasModel);
                    NasExportVO nasExportVO = BeanUtil.copyProperties(recoveryNasModel, NasExportVO.class);
                    nasExportVO.setStorageSize(nasExportVO.getStorageSize() + "GB");
                    nasListExt.add(nasExportVO);
                });
                resourceExport.setNasExportVOS(nasListExt);
                break;
            case "pm":
                List<PmExportVO> pmListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryPmModel recoveryPmModel = JSON.parseObject(propertySnapshot, RecoveryPmModel.class);
                    commonFill(productDTO, recoveryPmModel);
                    PmExportVO pmExportVO = BeanUtil.copyProperties(recoveryPmModel, PmExportVO.class);
                    pmExportVO.setVCpus(recoveryPmModel.getVCpus() + "核");
                    pmExportVO.setGpuNum(recoveryPmModel.getGpuNum() + "张");
                    pmListExt.add(pmExportVO);
                });
                resourceExport.setPmExportVOS(pmListExt);
                break;
            case "kafka":
                List<KafkaExportVO> kafkaListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryKafkaModel recoveryKafkaModel = JSON.parseObject(propertySnapshot, RecoveryKafkaModel.class);
                    commonFill(productDTO, recoveryKafkaModel);
                    KafkaExportVO kafkaExportVO = BeanUtil.copyProperties(recoveryKafkaModel, KafkaExportVO.class);
                    kafkaListExt.add(kafkaExportVO);
                });
                resourceExport.setKafkaExportVOS(kafkaListExt);
                break;
            case "flink":
                List<FlinkExportVO> flinkListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryFlinkModel recoveryFlinkModel = JSON.parseObject(propertySnapshot, RecoveryFlinkModel.class);
                    commonFill(productDTO, recoveryFlinkModel);
                    FlinkExportVO flinkExportVO = BeanUtil.copyProperties(recoveryFlinkModel, FlinkExportVO.class);
                    flinkListExt.add(flinkExportVO);
                });
                resourceExport.setFlinkExportVOS(flinkListExt);
                break;
            case "es":
                List<EsExportVO> esListExt = Lists.newArrayList();
                productDTOS.forEach(productDTO -> {
                    String propertySnapshot = productDTO.getPropertySnapshot();
                    RecoveryEsModel recoveryEsModel = JSON.parseObject(propertySnapshot, RecoveryEsModel.class);
                    commonFill(productDTO, recoveryEsModel);
                    EsExportVO esExportVO = BeanUtil.copyProperties(recoveryEsModel, EsExportVO.class);
                    esListExt.add(esExportVO);
                });
                resourceExport.setEsExportVOS(esListExt);
                break;
            default:
        }

        return resourceExport;
    }

    default void fillDataDisk(RecoveryEcsModel recoveryEcsModel){
        List<RecoveryEvsModel> evsModelList = recoveryEcsModel.getEvsModelList();
        String dataDisks = "";
        if (CollectionUtil.isNotEmpty(evsModelList)) {
            dataDisks = evsModelList.stream().map(RecoveryEvsModel::getDataDisk).filter(StringUtils::isNotEmpty).collect(Collectors.joining(","));
        }
        recoveryEcsModel.setDataDisks(dataDisks);
    }

    default void fillBackup(RecoveryBackupModel recoveryBackupModel){
        String backupType = recoveryBackupModel.getBackupType();
        if ("ECS".equals(backupType)) {
            backupType = "云主机";
        } else if ("EVS".equals(backupType)) {
            backupType = "云硬盘";
        }
        recoveryBackupModel.setBackupType(backupType);
        String frequency = recoveryBackupModel.getFrequency();
        if ("days".equals(frequency)) {
            frequency = "每天";
        } else if ("weeks".equals(frequency)) {
            frequency = "每周";
        }
        recoveryBackupModel.setFrequency(frequency);
    }

    default void commonFill(RecoveryWorkOrderProductDTO productDTO, BaseReconveryProductModel model) {
        model.setTenantConfirm(productDTO.getTenantConfirm());
        model.setHcmStatus(productDTO.getHcmStatus());
        model.setId(productDTO.getId());
        model.setRecoveryStatus(productDTO.getRecoveryStatus());
        model.setMessage(productDTO.getMessage());
        model.setApplyTimeCn(ApprovalTimeEnum.getByCode(model.getApplyTime()).getName());
    }
}
