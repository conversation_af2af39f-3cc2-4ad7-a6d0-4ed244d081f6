package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:55:56
 */
@Data
public class RecoveryVpcModel extends BaseReconveryProductModel {

    //vpcId
    private String vpcId;

    //vpc名称
    @ExcelExportHeader(value = "vpc名称")
    private String vpcName;

    //云类型
    private String catalogueDomainCode;

    private String catalogueDomainName;

    @ExcelExportHeader(value = "网段")
    private String cidr;

    //子网个数
    private Integer subnetNum;

    //工单编号
    @ExcelExportHeader(value = "工单编号")
    private String orderCode;
    @ExcelExportHeader(value = "申请人")
    private String userName;





}
