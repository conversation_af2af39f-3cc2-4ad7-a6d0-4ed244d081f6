package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月05日 19:56:55
 */
@Data
public class EipModel extends BaseProductModel {


    /**
     * 挂载云主机id
     */
    private String vmId;

//    private String deviceType;
    /**
     * vmName
     */
    private String vmName;

    //开通数量
    private Integer openNum;

    private String productType;

    /**
     * 是否绑定公网IP
     */
    private Boolean bindPublicIp;

    private Integer bandwidth;

    private String gId;

    /**
     * 申请时长
     */
    private String applyTime;


    /**
     * eipName
     */
    private String eipName;

}
