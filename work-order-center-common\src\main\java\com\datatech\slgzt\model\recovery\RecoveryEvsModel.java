package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:55:56
 */
@Data
public class RecoveryEvsModel extends BaseReconveryProductModel {
    //数据盘id
    private String dataDiskId;

    //数据盘名称
    private String dataDiskName;

    /**
     * 数据盘
     */
    @ExcelExportHeader(value = "数据盘")
    private String dataDisk;

    //数据盘挂载VmId
    private String vmId;

    //数据盘挂载VmName
    @ExcelExportHeader(value = "云主机")
    private String vmName;

    //是否挂载
    @ExcelExportHeader(value = "是否挂载云主机")
    private Boolean mountVm;



}
