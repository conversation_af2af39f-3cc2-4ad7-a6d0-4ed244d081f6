package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

import java.util.List;

/**
 * 回收的ecs模型
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:45:55
 */
@Data
public class RecoveryEcsModel extends BaseReconveryProductModel {
    //主机资源id
    private String vmId;

    //主机名称
    @ExcelExportHeader(value = "主机名称")
    private String vmName;

    //镜像类型 比如 centos
    private String imageType;

    //镜像版本
    @ExcelExportHeader(value = "系统版本")
    private String osVersion;

    //规格类型
    @ExcelExportHeader(value = "实例规格")
    private String spec;

    //系统盘
    @ExcelExportHeader(value = "系统盘")
    private String sysDisk;

    //todo 数据盘,多个时进行拼接
    private List<RecoveryEvsModel> evsModelList;

    //TODO 弹性ip
    private RecoveryEipModel eipModel;

    //config
    private String cmdbId;

    //manageId

    private String manageIp;

    @ExcelExportHeader(value = "IP")
    private String ip;

    /**
     * 资源id
     */
    @ExcelExportHeader(value = "资源id")
    private String resourceId;

    @ExcelExportHeader(value = "数据盘")
    private String dataDisks;


}
