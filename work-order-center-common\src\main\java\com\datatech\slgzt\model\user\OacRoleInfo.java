package com.datatech.slgzt.model.user;

import lombok.Data;

import java.util.List;

/**
 * 角色详细信息
 *
 * <AUTHOR>
 * @date 2024/11/18 16:42
 **/
@Data
public class OacRoleInfo {

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 角色作用域
     */
    private String roleScope;

    /**
     * 角色作用对象编号
     */
    private Long roleEntityId;

    /**
     * 云平台
     */
    private String domainCode;

    /**
     * 角色所拥有的权限信息
     */
    private List<OacAuthority> authorities;
}