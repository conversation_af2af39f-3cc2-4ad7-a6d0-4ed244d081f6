package com.datatech.slgzt.model.usercenter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/1/7
 */

@Data
@Accessors(chain = true)
public class UserCenterAppendUserRoleDTO implements Serializable {

    /**
     * 用户id集
     */
    private List<Long> userIds;

    /**
     * 角色id集
     */
    private List<Long> roleIds;
}

