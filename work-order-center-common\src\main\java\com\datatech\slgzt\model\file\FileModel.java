package com.datatech.slgzt.model.file;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class FileModel implements Serializable {

    /**
     * 订单附件类型
     * {@link com.cmp.oac.portalcenter.common.enums.FileTypeEnum}
     */
    @NotBlank(message = "订单文件类型不能为空")
    private String orderFileType;

    /**
     * 附件编号
     */
    @NotNull(message = "附件编号不能为空")
    private String fileId;

    /**
     * 附件排序
     */
    private Integer sort;

    /**
     * 资源费用
     */
    private Double cost;

    /**
     * 开通资源数量
     */
    private Integer resourceNum;

    /**
     * 云平台
     */
    private String domainCode;

}
