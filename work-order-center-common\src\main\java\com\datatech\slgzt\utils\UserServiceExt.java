package com.datatech.slgzt.utils;

import com.datatech.slgzt.enums.AuthorityCodeEnum;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;


import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月04日 15:24:33
 */
public class UserServiceExt {

    public static void processRoleData(List<UserCenterRoleDTO> roles) {
        if (ObjNullUtils.isNotNull(roles)) {
            List<String> roleCodes = roles.stream().map(UserCenterRoleDTO::getCode).collect(Collectors.toList());
            if (roleCodes.contains(AuthorityCodeEnum.OPERATION_GROUP.code())) {
                if (!roleCodes.contains(AuthorityCodeEnum.RESOURCE_CREATION.code())) {
                    UserCenterRoleDTO role = new UserCenterRoleDTO();
                    role.setCode(AuthorityCodeEnum.RESOURCE_CREATION.code());
                    role.setName(AuthorityCodeEnum.RESOURCE_CREATION.name());
                    roles.add(role);
                }
                if (!roleCodes.contains(AuthorityCodeEnum.NETWORK_PROVISIONING.code())) {
                    UserCenterRoleDTO role = new UserCenterRoleDTO();
                    role.setCode(AuthorityCodeEnum.NETWORK_PROVISIONING.code());
                    role.setName(AuthorityCodeEnum.NETWORK_PROVISIONING.name());
                    roles.add(role);
                }
                // 添加回收相关的交维清退、资源回收和网络回收相关角色
                if (!roleCodes.contains(AuthorityCodeEnum.RETREAT_DIMENSION.code())) {
                    UserCenterRoleDTO role = new UserCenterRoleDTO();
                    role.setCode(AuthorityCodeEnum.RETREAT_DIMENSION.code());
                    role.setName(AuthorityCodeEnum.RETREAT_DIMENSION.name());
                    roles.add(role);
                }
                if (!roleCodes.contains(AuthorityCodeEnum.RESOURCE_RECOVERY.code())) {
                    UserCenterRoleDTO role = new UserCenterRoleDTO();
                    role.setCode(AuthorityCodeEnum.RESOURCE_RECOVERY.code());
                    role.setName(AuthorityCodeEnum.RESOURCE_RECOVERY.name());
                    roles.add(role);
                }
                if (!roleCodes.contains(AuthorityCodeEnum.NETWORK_RECOVERY.code())) {
                    UserCenterRoleDTO role = new UserCenterRoleDTO();
                    role.setCode(AuthorityCodeEnum.NETWORK_RECOVERY.code());
                    role.setName(AuthorityCodeEnum.NETWORK_RECOVERY.name());
                    roles.add(role);
                }
            }

            if (roleCodes.contains(AuthorityCodeEnum.USER_TASK.code())
                    && !roleCodes.contains(AuthorityCodeEnum.TENANT_TASK.code())) {
                UserCenterRoleDTO role = new UserCenterRoleDTO();
                role.setCode(AuthorityCodeEnum.TENANT_TASK.code());
                role.setName(AuthorityCodeEnum.TENANT_TASK.name());
                roles.add(role);
            }
            if (roleCodes.contains(AuthorityCodeEnum.TENANT_ADMIN.code()) || roleCodes.contains(AuthorityCodeEnum.FREE_USER.code())) {
                UserCenterRoleDTO role = new UserCenterRoleDTO();
                role.setCode(AuthorityCodeEnum.TENANT_TASK.code());
                role.setName(AuthorityCodeEnum.TENANT_TASK.name());
                roles.add(role);
                UserCenterRoleDTO role2 = new UserCenterRoleDTO();
                role2.setCode(AuthorityCodeEnum.USER_TASK.code());
                role2.setName(AuthorityCodeEnum.USER_TASK.name());
                roles.add(role2);
            }

            if (roleCodes.contains(AuthorityCodeEnum.SUPER_ADMIN.code())) {
                List<AuthorityCodeEnum> authorityCodeEnumList = Arrays.asList(AuthorityCodeEnum.USER_TASK, AuthorityCodeEnum.TENANT_TASK, AuthorityCodeEnum.SCHEMA_ADMINISTRATOR,
                        AuthorityCodeEnum.BUSINESS_DEPART_LEADER, AuthorityCodeEnum.BUSINESS_DEPART_LEADER2, AuthorityCodeEnum.BUSINESS2_DEPART_LEADER,
                        AuthorityCodeEnum.CLOUD_LEADER, AuthorityCodeEnum.CLOUD_LEADER_2, AuthorityCodeEnum.NETWORK_PROVISIONING, AuthorityCodeEnum.RESOURCE_CREATION);
                for (AuthorityCodeEnum codeEnum : authorityCodeEnumList) {
                    UserCenterRoleDTO role = new UserCenterRoleDTO();
                    role.setCode(codeEnum.code());
                    role.setName(codeEnum.name());
                    roles.add(role);
                }
            }
        }
    }


    public static List<String> processRoleData_(List<UserCenterRoleDTO> roles) {
        if (ObjNullUtils.isNull(roles)) return Collections.emptyList();

        // 使用 Set 存储角色 CODE 以提高查询效率
        Set<String> roleCodeSet = roles.stream()
                .map(UserCenterRoleDTO::getCode)
                .collect(Collectors.toSet());

        // 1. 处理 OPERATION_GROUP 相关角色
        if (roleCodeSet.contains(AuthorityCodeEnum.OPERATION_GROUP.code())) {
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.RESOURCE_CREATION);
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.NETWORK_PROVISIONING);
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.RETREAT_DIMENSION);
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.RESOURCE_RECOVERY);
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.NETWORK_RECOVERY);
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.RESOURCE_CHANGE);
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.SHUTDOWN);
        }

        // 2. 处理 USER_TASK 相关逻辑
        if (roleCodeSet.contains(AuthorityCodeEnum.USER_TASK.code())
                && !roleCodeSet.contains(AuthorityCodeEnum.TENANT_TASK.code())) {
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.TENANT_TASK);
        }

        // 3. 处理 TENANT_ADMIN 或 FREE_USER 逻辑
        if (roleCodeSet.contains(AuthorityCodeEnum.TENANT_ADMIN.code())
                || roleCodeSet.contains(AuthorityCodeEnum.FREE_USER.code())) {
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.TENANT_TASK);
            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.USER_TASK);
        }

        // 4. 处理 SUPER_ADMIN 逻辑
        if (roleCodeSet.contains(AuthorityCodeEnum.SUPER_ADMIN.code())) {
            Arrays.asList(
                    AuthorityCodeEnum.USER_TASK,
                    AuthorityCodeEnum.TENANT_TASK,
                    AuthorityCodeEnum.SCHEMA_ADMINISTRATOR,
                    AuthorityCodeEnum.BUSINESS_DEPART_LEADER,
                    AuthorityCodeEnum.BUSINESS_DEPART_LEADER2,
                    AuthorityCodeEnum.BUSINESS2_DEPART_LEADER,
                    AuthorityCodeEnum.CLOUD_LEADER,
                    AuthorityCodeEnum.CLOUD_LEADER_2,
                    AuthorityCodeEnum.NETWORK_PROVISIONING,
                    AuthorityCodeEnum.RESOURCE_CREATION,
//                    AuthorityCodeEnum.PROFESSIONAL_GROUP,
                    AuthorityCodeEnum.RESOURCE_CHANGE,
                    AuthorityCodeEnum.SHUTDOWN

            ).forEach(codeEnum -> addRoleIfAbsent(roles, roleCodeSet, codeEnum));
        }

//        // 5. 处理 主机专业组 相关角色
//        if (roleCodeSet.contains(AuthorityCodeEnum.PROFESSIONAL_GROUP.code())) {
//            addRoleIfAbsent(roles, roleCodeSet, AuthorityCodeEnum.SHUTDOWN);
//        }
        // 返回所有角色的 CODE
        return roles.stream().map(UserCenterRoleDTO::getCode).collect(Collectors.toList());
    }

    /**
     * 辅助方法：如果角色不存在则添加，并同步更新 Set
     */
    private static void addRoleIfAbsent(
            List<UserCenterRoleDTO> roles,
            Set<String> roleCodeSet,
            AuthorityCodeEnum codeEnum
    ) {
        if (!roleCodeSet.contains(codeEnum.code())) {
            UserCenterRoleDTO role = new UserCenterRoleDTO();
            role.setCode(codeEnum.code());
            role.setName(codeEnum.name());
            roles.add(role);
            roleCodeSet.add(codeEnum.code()); // 更新 Set
        }
    }


}
