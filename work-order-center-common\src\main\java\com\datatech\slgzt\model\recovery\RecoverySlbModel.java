package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:55:56
 */
@Data
public class RecoverySlbModel extends BaseReconveryProductModel {

    //obsId
    private String slbId;

    //规格名称
    @ExcelExportHeader(value = "实例规格")
    private String spec;
    @ExcelExportHeader(value = "负载均衡名称")
    private String slbName;

    private RecoveryEipModel eipModel;


}
