package com.datatech.slgzt.model.home;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 首页资源开通数实体类
 *
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/16
 */

@Data
@Accessors(chain = true)
public class HomeResourceOpenVO implements Serializable {

    /**
     * 商品类型
     */
    private String goodType;

    /**
     * 本月资源开通数
     *
     * @mack 0
     */
    private int amount = 0;

    /**
     * 上个月资源开通数
     */
    private int lastMount = 0;

    /**
     * 资源较上月变化数值（小于0则为减少， 大于0则为新增）
     *
     * @mock 0
     */
    private int diffAmount;

    /**
     * 资源较上月变化比率
     *
     * @mock 0%
     */
    private String ratio;

    public int getDiffAmount() {
        return Math.abs(amount - lastMount);
    }


    public String getRatio() {
        // 计算变化百分比
        if (amount == 0 && lastMount == 0) {
            return "0";
        }
        // 避免除零错误,上个月为0
        if (lastMount == 0) {
            return "N/A";
        }

        // 计算变化百分比
        double percentageChange = (double) (amount - lastMount) / lastMount * 100;
        // 格式化输出，保留两位小数
        return String.format("%.2f%%", percentageChange);
    }




    public void calculatePercentageChange(int thisMonth, int lastMonth) {
        if (lastMonth == 0 && thisMonth == 0) {
            this.ratio = "0";
            return;
        }

        this.diffAmount = Math.abs(thisMonth - lastMonth);

        // 避免除零错误,上个月为0
        if (lastMonth == 0) {
            this.ratio = "N/A";
            return;
        }

        // 计算变化百分比
        double percentageChange = (double) (thisMonth - lastMonth) / lastMonth * 100;

        // 格式化输出，保留两位小数
        this.ratio = String.format("%.2f%%", percentageChange);
    }
}

