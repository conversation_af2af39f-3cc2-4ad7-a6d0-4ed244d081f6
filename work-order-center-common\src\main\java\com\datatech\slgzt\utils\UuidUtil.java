package com.datatech.slgzt.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

public class UuidUtil {

    public static String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 根据当前年月日，以及随机生成5个数，组成订单编码
     *
     * @return
     */
    public static String getOrderCode() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateNowStr = sdf.format(new Date());
        dateNowStr = dateNowStr + getRandom(5);
        return dateNowStr;
    }

    /**
     * 获取N位随机数
     *
     * @param n
     * @return
     */
    private static long getRandom(long n) {
        long min = 1, max = 9;
        for (int i = 1; i < n; i++) {
            min *= 10;
            max *= 10;
        }
        long rangeLong = (((long) (new Random().nextDouble() * (max - min)))) + min;
        return rangeLong;
    }

    public static String getUUID() {
        String id = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);
        return id;
    }

    public static String getGid(String productType) {
        return productType+"_"+System.currentTimeMillis()+Math.round((Math.random()+1) * 1000);
    }

}
