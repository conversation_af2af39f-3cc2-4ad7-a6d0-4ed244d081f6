package com.datatech.slgzt.model.order;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 简化服务组和产品属性
 *
 * <AUTHOR>
 */
@Data
public class ExtentProductOrderAttr implements Serializable {

    /**
     * 产品类型
     */
    @NotBlank(message = "产品类型不能为空")
    private String productType;

    /**
     * 该字段只针对于数据盘分组生成多个产品
     */
    private String evsProductType;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 实例规则、系统盘数据盘时需要传递该值
     */
    private String serviceType;

    /**
     * 属性编号，同时当code为实例规则、系统盘数据盘时表示的是value，即服务id（下拉框中对于的id）
     */
    private Long attrId;

    /**
     * 属性编码
     */
    @NotBlank(message = "属性编码不能为空")
    private String attrCode;

    /**
     * 属性名称
     */
    @NotBlank(message = "属性名称不能为空")
    private String attrName;

    /**
     * 同时当code为实例规则、系统盘数据盘时，判断serviceType值范围
     */
    private String value;
}
