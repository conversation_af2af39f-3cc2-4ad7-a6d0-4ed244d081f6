package com.datatech.slgzt.utils;


import io.jsonwebtoken.*;
import lombok.Data;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.security.Key;
import java.util.Date;

/**
 * 生成token令牌
 *
 * <AUTHOR>
 */
public class JwtTokenUtil {

    private final static String base64Secret = "szaisinoMDk4ZjZiY2Q0NjIxZDM3M2NhZGU0ZTgzMjYyN2I0ZjY=";




    public static Claims parseTokenJWT(String jsonWebToken) {
        //不管是否过期，都返回claims对象
        Claims claims;
        try {
            claims = Jwts.parser()
                    .setSigningKey(DatatypeConverter.parseBase64Binary(base64Secret)) // 设置标识名
                    .parseClaimsJws(jsonWebToken)  //解析token
                    .getBody();
        } catch (ExpiredJwtException e) {
            e.printStackTrace();
            claims = e.getClaims();
        }
        return claims;
    }

    @Data
    public static class TokenParam {

        /**
         * 用户编号
         */
        private Long userId;

        /**
         * 组织架构编号
         */
        private Long orgId;

        /**
         * 租户编号
         */
        private Long tenantId;

        /**
         * 登陆账户
         */
        private String loginName;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 用户类型
         */
        private String userType;

        /**
         * 版本号
         */
        private String versionId;

        /**
         * 兼容老版本菜单
         */
        private String menuType;

    }

}