package com.datatech.slgzt.utils;

import cn.hutool.extra.ftp.FtpConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;

import javax.annotation.Resource;
import java.io.*;

/**
 * <AUTHOR>
 * @date 2024/11/18 16:32
 */
@Data
@Slf4j
public class FTPUtil {
    @Resource
    private static FtpConfig ftpConfig;

    private static Integer maxSize = 1024000;
    private static Integer bufferSize = 1024 * 1024;
    private String ip;
    private int port;
    private String user;
    private String pwd;
    private FTPClient ftpClient;

    public FTPUtil(String ip, int port, String user, String pwd) {
        this.ip = ip;
        this.port = port;
        this.user = user;
        this.pwd = pwd;
    }

    private boolean connectServer(String ip, int port, String user, String pwd) {
        ftpClient = new FTPClient();
        Boolean isSuccess = false;
        try {
            ftpClient.connect(ip, port);
            isSuccess = ftpClient.login(user, pwd);
            int reply = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                throw new RuntimeException("连接ftp服务器失败,code:" + reply);
            }
        } catch (IOException e) {
            throw new RuntimeException("连接ftp服务器失败:" + e.getMessage());
        }
        return isSuccess;
    }

    public boolean uploadFile(String remotePath, File fileItem) throws IOException {
        boolean upload = true;
        FileInputStream fileInputStream = null;
        //connect to ftpServer
        if (connectServer(this.ip, this.port, this.user, this.pwd)) {
            try {
                boolean makeDirectory = ftpClient.makeDirectory(remotePath);
                log.info("makeDirectory:{}", makeDirectory);
                boolean changeWorkingDirectory = ftpClient.changeWorkingDirectory(remotePath);
                log.info("remotePath:{}", remotePath);
                log.info("changeWorkingDirectory:{}", changeWorkingDirectory);
                log.info("pwd:{}", ftpClient.printWorkingDirectory());
                ftpClient.setBufferSize(bufferSize);
                ftpClient.setControlEncoding("UTF-8");
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                ftpClient.enterLocalPassiveMode();
                fileInputStream = new FileInputStream(fileItem);
                ftpClient.storeFile(fileItem.getName(), fileInputStream);
            } catch (IOException e) {
                log.error("上传文件异常", e);
                throw new RuntimeException("上传文件异常:" + e.getMessage());
            } finally {
                fileInputStream.close();
                ftpClient.disconnect();
            }
        }
        return upload;
    }

    /**
     * 下载文件
     *
     * @param filePath 文件在服务器的路径
     * @return
     */
    public InputStream down(String filePath) throws IOException {
        FileOutputStream fos = null;
        InputStream inputStream = null;
        if (connectServer(this.ip, this.port, this.user, this.pwd)) {
            try {
                ftpClient.setBufferSize(bufferSize);
                //设置文件类型（二进制）
                ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
                String path = new String(filePath.replaceAll("//", "/").getBytes("UTF-8"), "iso-8859-1");
                ftpClient.enterLocalPassiveMode();
                inputStream = ftpClient.retrieveFileStream(path);
            } catch (Exception e) {
                e.printStackTrace();
                throw new RuntimeException("FTP客户端出错！", e);
            } finally {
                IOUtils.closeQuietly(fos);
                try {
                    ftpClient.disconnect();
                } catch (IOException e) {
                    e.printStackTrace();
                    throw new RuntimeException("关闭FTP连接发生异常！", e);
                }
            }
        }
        return inputStream;
    }
}

