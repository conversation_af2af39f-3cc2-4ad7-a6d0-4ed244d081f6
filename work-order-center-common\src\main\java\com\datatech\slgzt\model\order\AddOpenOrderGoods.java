package com.datatech.slgzt.model.order;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.enums.ProductTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 开通资源商品订单-DTO类
 *
 * <AUTHOR>
 * @Date: 2024/11/18 15:49
 * @Description:
 */
@Data
public class AddOpenOrderGoods implements Serializable {

    /**
     * 商品编号
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String goodsName;

    /**
     * 商品类型
     */
    @NotBlank(message = "商品类型不能为空")
    private String goodsType;

    /**
     * 开通数量
     */
    @NotNull(message = "开通数量不能为空")
    private Integer numbers;

    /**
     * 云硬盘挂载的资源中心云主机vmId
     */
    private String vmId;
    /**
     * 云硬盘挂载的资源中心云主机名称
     */
    private String vmName;

    /**
     * 产品集合，提交时废弃下面对象
     *
     * @ignore·
     */
    @Valid
    @Deprecated
    private List<AddOpenOrderProduct> products;

    /**
     * 系统盘
     */
    private ExtentProductOrderAttr sysDisk;

    /**
     * 数据盘,支持添加多个数据盘
     */
    private List<ExtentProductOrderAttr> evs;

    /**
     * 带宽大小
     */
    private ExtentProductOrderAttr eip;

    /**
     * 主机配置-实例规则
     */
    private ExtentProductOrderAttr ecs;

    /**
     * 安全域
     */
    private ExtentProductOrderAttr securityDomain;

    /**
     * 镜像-操作系统
     */
    private ExtentProductOrderAttr imageOs;

    /**
     * 网络平面
     */
    private ExtentProductOrderAttr plane;

    /**
     * 主机名称
     */
    private ExtentProductOrderAttr instanceName;

    /**
     * 申请时长
     */
    private ExtentProductOrderAttr time;

    /**
     * 功能模块
     */
    private ExtentProductOrderAttr functionalModule;

    /**
     * 镜像版本
     */
    private ExtentProductOrderAttr imageVersion;

    /**
     * 负载均衡
     */
    private ExtentProductOrderAttr slb;

    /**
     * obs
     */
    private ExtentProductOrderAttr obs;

    /**
     * GPU云主机
     */
    private ExtentProductOrderAttr gcs;

    /**
     * nat网关
     */
    private ExtentProductOrderAttr nat;

    /**
     * 是否支持容灾 0 否 1 是
     */
    private Integer disasterRecovery;

    /**
     * 用于判断时在临时存放方便遍历
     */
    private transient List<ExtentProductOrderAttr> extentProductOrderAttrDTOList = new ArrayList<>();

    public void addExtentProductOrderAttr(String goodsType) {
        List<ExtentProductOrderAttr> list = Arrays.asList(sysDisk, ecs, securityDomain, imageOs, plane, instanceName, time, functionalModule, imageVersion, eip, slb, obs, gcs, nat);
        this.extentProductOrderAttrDTOList = new ArrayList<>(list);
        if (CollectionUtil.isNotEmpty(evs)) {
            // 生成分组的标识
            for (ExtentProductOrderAttr item : evs) {
                item.setEvsProductType(item.getProductType() + UUID.randomUUID());
            }
            extentProductOrderAttrDTOList.addAll(evs);
        }

        extentProductOrderAttrDTOList = extentProductOrderAttrDTOList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (ProductTypeEnum.EVS.getCode().equals(goodsType)) {
            List<ExtentProductOrderAttr> extentList = new ArrayList<>();
            for (ExtentProductOrderAttr item : evs) {
                for (ExtentProductOrderAttr attrDTO : extentProductOrderAttrDTOList) {
                    if (attrDTO != null) {
                        attrDTO.setEvsProductType(item.getEvsProductType());
                    }
                    extentList.add(attrDTO);
                }
            }

            extentProductOrderAttrDTOList = new ArrayList<>();
            extentProductOrderAttrDTOList.addAll(extentList);
        } else {
            for (ExtentProductOrderAttr attrDTO : extentProductOrderAttrDTOList) {
                if (attrDTO != null && StringUtils.isEmpty(attrDTO.getEvsProductType())) {
                    attrDTO.setEvsProductType(attrDTO.getProductType());
                }
            }
        }
    }
}
