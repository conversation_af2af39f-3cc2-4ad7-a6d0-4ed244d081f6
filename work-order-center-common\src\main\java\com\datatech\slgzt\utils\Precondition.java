package com.datatech.slgzt.utils;


import com.datatech.slgzt.enums.GlobalExceptionEnum;
import com.datatech.slgzt.exception.UniversalException;

import java.util.Collection;
import java.util.function.Consumer;

public class Precondition {

    public static void checkArgument(boolean expression) {
        if (!expression) {
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION);
        }
    }

    public static void checkArgument(boolean expression, String message) {
        if (!expression) {
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), message);
        }
    }

    public static void checkArgument(boolean expression, String message, Consumer<String> consumer) {
        if (!expression) {
            if (consumer != null) {
                consumer.accept(message);
            }
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), message);
        }
    }

    public static void checkArgument(Object obj, String message) {
        if (obj == null) {
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), message);
        }
        if (obj instanceof String) {
            if (ObjNullUtils.isNull(obj)) {
                throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), message);
            }
        }
        if (obj instanceof Collection) {
            if (ObjNullUtils.isNull((Collection) obj)) {
                throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), message);
            }
        }
    }

    public static void checkArgument(Object obj, String message,Integer code) {
        if (obj == null) {
            throw UniversalException.build(code, message);
        }
        if (obj instanceof String) {
            if (ObjNullUtils.isNull(obj)) {
                throw UniversalException.build(code, message);
            }
        }
        if (obj instanceof Collection) {
            if (ObjNullUtils.isNull((Collection) obj)) {
                throw UniversalException.build(code, message);
            }
        }
    }

    public static void checkArgument(Object obj) {
        if (obj == null) {
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), GlobalExceptionEnum.PARAM_EXCEPTION.getMessage());
        }
        if (obj instanceof String) {
            if (ObjNullUtils.isNull((String) obj)) {
                throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), GlobalExceptionEnum.PARAM_EXCEPTION.getMessage());
            }
        }
        if (obj instanceof Collection) {
            if (ObjNullUtils.isNull((Collection) obj)) {
                throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), GlobalExceptionEnum.PARAM_EXCEPTION.getMessage());
            }
        }
    }
}
