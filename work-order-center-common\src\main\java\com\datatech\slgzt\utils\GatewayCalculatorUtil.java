package com.datatech.slgzt.utils;

import org.apache.commons.lang3.StringUtils;

import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class GatewayCalculatorUtil {

    public static String getGateway(String cidr) {
            if (StringUtils.isEmpty(cidr)) {
                return "";
            }
            String ip = cidr.split("/")[0];
            if (isIPv4(ip)) {
                return getGatewayIpv4(cidr);
            } else if (ip.contains(":")) {
                return getGatewayIpv6(cidr);
            }

        return "";
    }

    private static boolean isIPv4(String ip) {
        String ipv4Pattern = "^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$";
        Pattern pattern = Pattern.compile(ipv4Pattern);
        Matcher matcher = pattern.matcher(ip);
        return matcher.matches();
    }

    // 判断是否为IPv6
    private static boolean isIPv6(String ip) {
        String ipv6Pattern = "^([0-9a-fA-F]{1,4}:){1,7}([0-9a-fA-F]{1,4}|:)$";
        Pattern pattern = Pattern.compile(ipv6Pattern);
        Matcher matcher = pattern.matcher(ip);
        return matcher.matches();
    }

    /**
     * 根据CIDR格式计算网关（第一个可用地址）
     *
     * @param cidr
     * @return
     * @throws UnknownHostException
     */
    private static String getGatewayIpv4(String cidr) {
        try {
            // 分割CIDR为网络地址和前缀长度
            String[] parts = cidr.split("/");
            String network = parts[0];  // 网络地址部分，例如 "***********"
            int prefixLength = Integer.parseInt(parts[1]);  // 子网掩码前缀长度，例如 26

            // 计算子网掩码
            String subnetMask = getSubnetMask(prefixLength);

            // 将网络地址和子网掩码转为字节数组
            InetAddress networkAddress = InetAddress.getByName(network);
            InetAddress mask = InetAddress.getByName(subnetMask);

            byte[] networkBytes = networkAddress.getAddress();
            byte[] maskBytes = mask.getAddress();

            byte[] gatewayBytes = new byte[4];

            // 计算第一个可用地址，即网络地址 + 1
            for (int i = 0; i < 4; i++) {
                gatewayBytes[i] = (byte) (networkBytes[i] & maskBytes[i]);
            }

            // 设置第一个可用地址（网络地址的最后一个字节 + 1） 增加最后一个字节，表示第一个可用地址
            gatewayBytes[3] += 1;

            // 返回计算得到的网关地址
            return InetAddress.getByAddress(gatewayBytes).getHostAddress();
        } catch (Exception e) {
            return "";
        }

    }

    // 获取IPv6网段的第一个可用地址作为网关
    private static String getGatewayIpv6(String cidr) {
        String networkAddress = cidr.split("/")[0];
        try {
            // 解析输入的IPv6网络地址
            Inet6Address inet6Address = (Inet6Address) Inet6Address.getByName(networkAddress);

            byte[] addressBytes = inet6Address.getAddress();

            // 如果是 /64 前缀，我们知道第一个地址是地址的末尾置为1
            // 即最后一部分加1，保持其他部分不变

            // 设置最后一个字节的值为 1
            addressBytes[15] = (byte) (addressBytes[15] + 1);

            // 构建网关地址
            Inet6Address gatewayAddress = (Inet6Address) Inet6Address.getByAddress(addressBytes);

            return gatewayAddress.getHostAddress();
        } catch (Exception e) {
            return "";
        }
    }

    // 根据前缀长度计算子网掩码
    private static String getSubnetMask(int prefixLength) {
        int mask = (0xFFFFFFFF << (32 - prefixLength)) & 0xFFFFFFFF;
        return String.format("%d.%d.%d.%d", (mask >> 24) & 0xFF, (mask >> 16) & 0xFF, (mask >> 8) & 0xFF, mask & 0xFF);
    }
}
