package com.datatech.slgzt.model;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.enums.ProductTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月14日 18:25:03
 */
@Data
public class BaseReconveryProductModel {
    /**
     * 产品订单id
     */
    private Long productOrderId;

    //计费类型 day:按天计费；month:按月计费
    private String billType;

    //计费方式 quant：按量计费；require：按需计费
    private String chargeType;

    /**
     * 产品类型
     *
     * @see ProductTypeEnum
     */
    private String productType;

    /**
     * 可用区名称
     */
    private String azName;

    /**
     * 可用区Code
     */
    private String azCode;

    /**
     * 可用区id
     */
    private String azId;


    /**
     * 资源池id
     * 当工单是线下开通的时候，资源池id是空的
     */
    private String regionId;

    /**
     * 资源池Code
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    @ExcelExportHeader(value = "资源池")
    private String regionName;


    //到期时间
    @ExcelExportHeader(value = "到期时间")
    private LocalDateTime expireTime;

    //创建时间
    private LocalDateTime createTime;

    //申请时长
    private String applyTime;

    // 申请时长中文
    @ExcelExportHeader(value = "申请时长")
    private String applyTimeCn;

    //租户id
    private Long tenantId;

    //租户名称
    @ExcelExportHeader(value = "租户")
    private String tenantName;

    //业务系统名称
    @ExcelExportHeader(value = "业务系统")
    private String businessSystemName;

    //业务系统id
    private Long businessSystemId;

    //domainCode
    private String domainCode;

    //domainName
    @ExcelExportHeader(value = "所属云")
    private String domainName;

    /**
     * 计费号
     */
    @ExcelExportHeader(value = "计费号")
    private String billId;



    /**
     * 客户编码
     */
    private String customNo;

    /**
     * 资源详情表的id
     */
    private Long resourceDetailId;


    /*------ext field------*/
    private Boolean tenantConfirm;

    //退维
    private String hcmStatus;

    //回收状态
    private String recoveryStatus;

    private Long id;

    @ExcelExportHeader(value = "失败原因")
    private String message;

    //同步回收
    private Boolean syncRecovery;
    /**
     * 该资源的创建订单id
     */
    private String createOrderId;
    /**
     * 该资源的创建订单编号
     */
    private String createOrderCode;
    /**
     * 该资源的 资源中心id
     */
    private String deviceId;
    /**
     * 该资源的 订购人
     */
    private String applyUserName;
}
