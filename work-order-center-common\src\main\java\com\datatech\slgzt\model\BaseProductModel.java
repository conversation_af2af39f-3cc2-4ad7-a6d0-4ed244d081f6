package com.datatech.slgzt.model;

import com.datatech.slgzt.enums.ProductTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月14日 18:25:03
 */
@Data
public class BaseProductModel {

    private String dagId;

    private List<String> relDagIds;
    /**
     * vpc-vsubnet_ network-subnet_
     */
    private List<String> subnetDagIds;

    //-------上面是dag图相关的---------------------------

    //计费类型 day:按天计费；month:按月计费
    private String billType;

    //计费方式 quant：按量计费；require：按需计费
    private String chargeType;

    //金额
    private BigDecimal amount =BigDecimal.ZERO;

    //-------上面是计费相关的-------------------------------

    private String customNo;

    private String idHash;

    private Long id;

    private List<Long> mainIds;
    /**
     * 租户ID
     */
    private Long tenantId;

    private String tenantName;

    /**
     * 计费号
     */
    private String billId;


    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 功能模块
     */
    private String functionalModule;

    /**
     * 云类型名称
     */
    private String catalogueDomainName;

    /**
     * 云类型编码
     */
    private String catalogueDomainCode;

    /**
     * 云平台名称
     */
    private String domainName;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 产品类型
     *
     * @see ProductTypeEnum
     */
    private String productType;

    /**
     * 可用区名称
     */
    private String azName;

    /**
     * 可用区Code
     */
    private String azCode;

    /**
     * 可用区id
     */
    private Long azId;


    /**
     * 资源池id
     * 当工单是线下开通的时候，资源池id是空的
     */
    private Long regionId;

    /**
     * 资源池Code
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;



    //-----------ext---------------------
    //message
    private String message;

    //状态
    private String status;

    /**
     * 原始名称
     */
    private String originName;

    /**
     * 归档IP地址
     */
    private String archivedIp;
}
