package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import com.datatech.slgzt.utils.ObjNullUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:55:56
 */
@Data
public class RecoveryObsModel extends BaseReconveryProductModel {

    //obsId
    private String obsId;

    //obs名称
    @ExcelExportHeader(value = "对象存储名称")
    private String obsName;

    //规格类型
    @ExcelExportHeader(value = "实例规格")
    private String spec;

    @ExcelExportHeader(value = "容量")
    private String capacity;

    @ExcelExportHeader(value = "存储类型")
    private String storeType;


    /**
     *  @Mapping(target = "storeType", expression = "java(resourceDetailVO.getSpec() != null ? resourceDetailVO.getSpec().split(\" \")[0] : null)")
     *     @Mapping(target = "capacity", expression = "java(resourceDetailVO.getSpec() != null && resourceDetailVO.getSpec().split(\" \").length > 1 ? resourceDetailVO.getSpec().split(\" \")[1] : null)")
     * @return
     */
    public String getCapacity() {
       if (ObjNullUtils.isNotNull(spec)) {
           String[] split = spec.split(" ");
           if (split.length > 1) {
               return split[1];
           }
       }
         return null;
    }


    public String getStoreType() {
        if (ObjNullUtils.isNotNull(spec)) {
            String[] split = spec.split(" ");
            return split[0];
        }
        return null;
    }

}
