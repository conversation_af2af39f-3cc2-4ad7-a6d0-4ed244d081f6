package com.datatech.slgzt.impl.service.corporate;

import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.impl.service.xieyun.JobStepHelper;
import com.datatech.slgzt.manager.DgRecoveryOrderManager;
import com.datatech.slgzt.manager.DgRecoveryOrderProductManager;
import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.dto.DgRecoveryOrderProductDTO;
import com.datatech.slgzt.service.corporate.DgRecoveryResourceService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.step.tasklet.Tasklet;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月22日 10:23:50
 */
@Slf4j
@Configuration
public class DgRecoveryJobDef implements InitializingBean {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private List<DgRecoveryResourceService> corporateResOpenService;

    private final Map<String, DgRecoveryResourceService> serviceMap = Maps.newHashMap();

    @Resource
    private DgRecoveryJobListener dgRecoveryJobListener;

    @Resource
    private DgRecoveryOrderProductManager productManager;

    @Resource
    private DgRecoveryOrderManager orderManager;


    @Bean("dgProductRecoveryJob")
    public Job environmentCreationJob() {
        Job job = jobBuilderFactory.get("dgProductRecoveryJob").incrementer(new RunIdIncrementer())
                .listener(dgRecoveryJobListener)
                .start(dgProductRecoveryInit())
                .next(dgEcsRecovery())
                .next(dgGcsRecovery())
                .next(dgMysqlRecovery())
                .next(dgSlbRecovery())
                .next(dgNatRecovery())
                .next(dgEipRecovery())
                .next(dgEvsRecovery())
                .next(dgObsRecovery())
                .next(dgVpnRecovery())
                .next(dgBackupRecovery())
                .build();
        return new ReferenceJobFactory(job).createJob();
    }

    //初始化的step用来获取执行id 主动停止 但是只会有一次
    @Bean("dgProductRecoveryInit")
    public Step dgProductRecoveryInit() {
        return stepBuilderFactory.get("dgProductRecoveryInit").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String stepAlreadyExecuted = jobStepHelper.get("firstInit");
            //判断是否为空
            if (ObjNullUtils.isNull(stepAlreadyExecuted)) {
                //如果为空，说明是第一次执行
                jobStepHelper.put("firstInit", "true");
                //主动停止任务
                jobStepHelper.stop();
                return RepeatStatus.FINISHED;
            }
            return RepeatStatus.FINISHED;
        }).build();
    }

    @Bean("dgEcsRecovery")
    public Step dgEcsRecovery() {
        return stepBuilderFactory.get("dgEcsRecovery").tasklet(getProductTasklet(ProductTypeEnum.ECS)).build();
    }

    @Bean("dgGcsRecovery")
    public Step dgGcsRecovery() {
        return stepBuilderFactory.get("dgGcsRecovery").tasklet(getProductTasklet(ProductTypeEnum.GCS)).build();
    }

    @Bean("dgMysqlRecovery")
    public Step dgMysqlRecovery() {
        return stepBuilderFactory.get("dgMysqlRecovery").tasklet(getProductTasklet(ProductTypeEnum.RDS_MYSQL)).build();
    }

    @Bean("dgSlbRecovery")
    public Step dgSlbRecovery() {
        return stepBuilderFactory.get("dgSlbRecovery").tasklet(getProductTasklet(ProductTypeEnum.SLB)).build();
    }


    @Bean("dgNatRecovery")
    public Step dgNatRecovery() {
        return stepBuilderFactory.get("dgNatRecovery").tasklet(getProductTasklet(ProductTypeEnum.NAT)).build();
    }

    @Bean("dgEipRecovery")
    public Step dgEipRecovery() {
        return stepBuilderFactory.get("dgEipRecovery").tasklet(getProductTasklet(ProductTypeEnum.EIP)).build();
    }

    @Bean("dgEvsRecovery")
    public Step dgEvsRecovery() {
        return stepBuilderFactory.get("dgEvsRecovery").tasklet(getProductTasklet(ProductTypeEnum.EVS)).build();
    }

    @Bean("dgObsRecovery")
    public Step dgObsRecovery() {
        return stepBuilderFactory.get("dgObsRecovery").tasklet(getProductTasklet(ProductTypeEnum.OBS)).build();
    }

    @Bean("dgVpnRecovery")
    public Step dgVpnRecovery() {
        return stepBuilderFactory.get("dgVpnRecovery").tasklet(getProductTasklet(ProductTypeEnum.VPN)).build();
    }

    @Bean("dgBackupRecovery")
    public Step dgBackupRecovery() {
        return stepBuilderFactory.get("dgBackupRecovery").tasklet(getProductTasklet(ProductTypeEnum.BACKUP)).build();
    }

    private Tasklet getProductTasklet(ProductTypeEnum productType) {
        return (contribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String orderId = jobStepHelper.get("orderId");
            // 根据orderId，获取dgRecoveryOrder
            DgRecoveryOrderDTO orderDTO = orderManager.getById(orderId);
            // 通过orderId获取到product对象
            List<DgRecoveryOrderProductDTO> productDTOs =
                    productManager.listByWorkOrderId(orderId).stream()
                            .filter(i -> productType.getCode().equals(i.getProductType()))
                            .collect(Collectors.toList());
            //如果productDTOs为空，说明没有相关的资源，直接返回
            if (ObjNullUtils.isNull(productDTOs)) {
                return RepeatStatus.FINISHED;
            }
            // 找一个product，进行开通
            Optional<DgRecoveryOrderProductDTO> productDTOOptional = filterProduct(productDTOs);
            if (productDTOOptional.isPresent()) {
                DgRecoveryResourceService createService = serviceMap.get(productType.getCode());
                DgRecoveryOrderProductDTO productDTO = productDTOOptional.get();
                try {
                    // 开通
                    createService.recoveryResource(orderDTO, Collections.singletonList(productDTO));
                } catch (Exception e) {
                    log.warn("product:{} , error message:{}", productDTO, ExceptionUtils.getStackTrace(e));
                    DgRecoveryOrderProductDTO dto = new DgRecoveryOrderProductDTO();
                    dto.setId(productDTO.getId());
                    dto.setRecoveryStatus(RecoveryStatusEnum.RECLAIM_FAILURE.getType());
                    dto.setMessage(e.getMessage());
                    productManager.update(dto);
                    // 发短信
//                    orderManager.sendFailSms(productDTO.getId());
                }
                // 开通后stop，等待product开通成功后，回调restart
                jobStepHelper.stop();
            }
            return RepeatStatus.FINISHED;
        };
    }


    @Override
    public void afterPropertiesSet() {
        for (DgRecoveryResourceService service : corporateResOpenService) {
            serviceMap.put(service.registerOpenService().getCode(), service);
        }
    }

    private Optional<DgRecoveryOrderProductDTO> filterProduct(List<DgRecoveryOrderProductDTO> productDTOs) {
        return productDTOs.stream().filter(i ->
                i.getParentProductId() == 0
                        && (// 没有回收状态 或者 带回收/回收失败
                        i.getRecoveryStatus() == null ||
                                (RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().equals(i.getRecoveryStatus())
                                        || RecoveryStatusEnum.RECLAIM_FAILURE.getType().equals(i.getRecoveryStatus())))
        ).findFirst();
    }
}
