package com.datatech.slgzt.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Stream;

/**
 * @description 建议使用 LocalDateTime 及 LocalDate 处理时间
 */
public class DateUtils {

    public static final String DATE_TIME_PATTERN1_Z = "yyyy-MM-dd'T'HH:mm:ss.SSS Z";
    public static final String DATE_TIME_yyyyMMdd = "yyyyMMdd";
    public static final String DATE_TIME_yyyyMM = "yyyyMM";
    public static final String DATE_TIME_yyyy_MM = "yyyy-MM";


    public static final String DATE_TIME_YYYY_MM_DD_HH = "yyyyMMddHH";
    public static final String DATE_FORMAT_FULL = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_YMD = "yyyy-MM-dd";
    public static final String DATE_FORMAT_HMS = "HH:mm:ss";
    public static final String DATE_FORMAT_HM = "HH:mm";
    public static final String DATE_FORMAT_YMDHM = "yyyy-MM-dd HH:mm";
    public static final String DATE_FORMAT_YMDHMS = "yyyyMMddHHmmss";
    public static final long ONE_DAY_MILLS = 3600 * 1000 * 24;
    public static final int WEEK_DAYS = 7;
    private static final int DATE_LENGTH = DATE_FORMAT_YMDHM.length();

    private static final SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_FULL);
    private static final SimpleDateFormat sdf1 = new SimpleDateFormat(DATE_TIME_PATTERN1_Z);
    private static final SimpleDateFormat sdf_yyyyMMdd = new SimpleDateFormat(DATE_TIME_yyyyMMdd);
    private static final SimpleDateFormat sdf_yyyy_MM_dd = new SimpleDateFormat(DATE_FORMAT_YMD);
    private static final SimpleDateFormat sdf_yyyyMM = new SimpleDateFormat(DATE_TIME_yyyyMM);

    public static Date toDate(LocalDate localDate) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    public static Date toDate(String str, String pattern) {
        LocalDate localDate = toLocalDate(str, pattern);
        return toDate(localDate);
    }

    public static Date toDate(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    public static LocalDate toLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        return localDateTime.toLocalDate();
    }

    public static String ofYearMonth(LocalDateTime localDateTime) {
        int year = localDateTime.getYear();
        int monthValue = localDateTime.getMonthValue();
        if (monthValue < 10) {
            return year + "0" + monthValue;
        }
        return year + "" + monthValue;
    }

    public static LocalDateTime timestamp2LocalDateTime(Long timestamp) {
        LocalDate localDate = Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDate();
        return Instant.ofEpochMilli(timestamp).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
    }


    public static LocalDateTime toLocalDateTime(String str, String pattern) {
        return LocalDateTime.parse(str, DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 将字符串类型日期转换为 LocalDate 类型
     *
     * @param str     日期。例如 2021-12-10
     * @param pattern 解析模式。例如 yyyy-MM-dd
     * @return LocalDate
     */
    public static LocalDate toLocalDate(String str, String pattern) {
        return LocalDate.parse(str, DateTimeFormatter.ofPattern(pattern));
    }

    public static String toString(LocalDateTime localDateTime) {
        return null == localDateTime ? "" : localDateTime.format(DateTimeFormatter.ofPattern(sdf_yyyyMMdd.toPattern()));
    }

    /**
     * 将 LocalDateTime 转换为字符串
     *
     * @param localDateTime 时间
     * @param pattern       期望时间格式
     * @return String
     */
    public static String toString(LocalDateTime localDateTime, String pattern) {
        return null == localDateTime ? "" : localDateTime.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 将 LocalDate 转换为字符串
     *
     * @param localDate 日期
     * @param pattern   期望日期格式
     * @return String
     */
    public static String toString(LocalDate localDate, String pattern) {
        return null == localDate ? "" : localDate.format(DateTimeFormatter.ofPattern(pattern));
    }

    public static String current(String pattern) {
        return DateUtils.toString(LocalDateTime.now(), pattern);
    }

    public static LocalDateTime toLocalDateTime(long timestamp) {
        return timestamp == 0 ? null : LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), TimeZone.getDefault().toZoneId());
    }

    public static String toString(long timestamp, String pattern) {
        LocalDateTime localDateTime = toLocalDateTime(timestamp);
        return toString(localDateTime, pattern);
    }

    /**
     * 获取上周第一天
     *
     * @param localDate 时间日期
     * @return LocalDate
     */
    public static LocalDate ofLastWeekStartDay(LocalDate localDate) {
        int value = localDate.getDayOfWeek().getValue();
        if (value == 7) {
            return localDate.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
        }
        return localDate.minusDays(7).with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
    }

    /**
     * 获取上周最后一天
     *
     * @param localDate 时间日期
     * @return LocalDate
     */
    public static LocalDate ofLastWeekEndDay(LocalDate localDate) {
        return localDate.minusDays(7).with(TemporalAdjusters.next(DayOfWeek.SATURDAY));
    }

    /**
     * 获取本周最后一天
     *
     * @param localDate 时间日期
     * @return LocalDate
     */
    public static LocalDate ofCurrentWeekEndDay(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.next(DayOfWeek.SATURDAY));
    }

    /**
     * 获取上个月第一天
     *
     * @param localDate 时间日期
     * @return LocalDate
     */
    public static LocalDate ofLastMonthStartDay(LocalDate localDate) {
        if (localDate.getMonthValue() == 1) {
            return LocalDate.of(localDate.getYear() - 1, 12, 1);
        }
        return LocalDate.of(localDate.getYear(), localDate.getMonthValue() - 1, 1);
    }

    /**
     * 获取上个月最后一天
     *
     * @param localDate 时间日期
     * @return LocalDate
     */
    public static LocalDate ofLastMonthEndDay(LocalDate localDate) {
        if (localDate.getMonthValue() == 1) {
            LocalDate lastMonth = LocalDate.of(localDate.getYear() - 1, 12, 1);
            return lastMonth.with(TemporalAdjusters.lastDayOfMonth());
        }
        LocalDate lastMonth = LocalDate.of(localDate.getYear(), localDate.getMonthValue() - 1, 1);
        return lastMonth.with(TemporalAdjusters.lastDayOfMonth());
    }

    /**
     * 获取起止日期间每一天日期列表
     *
     * @param start 起始日期。如 2021-12-06
     * @param end   结束日期。如 2021-12-24
     * @return list
     */
    public static List<String> betweenDates(String start, String end) {
        List<String> list = new ArrayList<>();
        LocalDate startDate = LocalDate.parse(start);
        LocalDate endDate = LocalDate.parse(end);
        long distance = ChronoUnit.DAYS.between(startDate, endDate);
        if (distance < 1) {
            return list;
        }
        Stream.iterate(startDate, d -> d.plusDays(1)).limit(distance + 1).forEach(f -> list.add(f.toString()));
        return list;
    }

    /**
     * 获取起止日期间每一天日期列表
     *
     * @param start 起始日期。如 2021-12-06
     * @param end   结束日期。如 2021-12-24
     * @return list
     */
    public static List<String> betweenDates(LocalDate start, LocalDate end) {
        List<String> list = new ArrayList<>();
        long distance = ChronoUnit.DAYS.between(start, end);
        if (distance < 1) {
            return list;
        }
        Stream.iterate(start, d -> d.plusDays(1)).limit(distance + 1).forEach(f -> list.add(f.toString()));
        return list;
    }

    /**
     * 将原时间中的 `-` 分隔符替换为 `/`。如 2021/12/06
     *
     * @param localDate 日期
     * @param pattern   日期格式
     * @return string
     */
    public static String replace(LocalDate localDate, String pattern) {
        return localDate.format(DateTimeFormatter.ofPattern(pattern)).replace("-", "/");
    }

    /**
     * 获取日期所在年的第一天
     *
     * @param localDate 日期
     * @return LocalDate 日期
     */
    public static LocalDate yearStart(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.firstDayOfYear());
    }

    /**
     * 获取日期所在年的最后一天
     *
     * @param localDate 日期
     * @return LocalDate 日期
     */
    public static LocalDate yearEnd(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.lastDayOfYear());
    }

    /**
     * 时间转化为毫秒数
     *
     * @param localDateTime 时间
     * @return Long 毫秒数
     */
    public static Long ofMilliseconds(LocalDateTime localDateTime) {
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 毫秒转化为日期
     *
     * @param milliseconds 毫秒
     * @return LocalDate 日期
     */
    public static LocalDate toLocalDate(Long milliseconds) {
        return Instant.ofEpochMilli(milliseconds).atZone(ZoneId.systemDefault()).toLocalDate();
    }


    public static LocalDateTime processGoodsExpireTime(LocalDateTime dateTime, String time) {
        //1个月,one_month
        //3个月,three_months
        //6个月,six_months
        //1年,one_year
        //2年,two_years

        //包月开通
        if ("one_month".equals(time)) {
            //返回当前时间加一个月
            dateTime = dateTime.plusMonths(1);
        }
        if ("three_months".equals(time)) {
            //返回当前时间加三个月
            dateTime = dateTime.plusMonths(3);
        }
        if ("six_months".equals(time)) {
            //返回当前时间加六个月
            dateTime = dateTime.plusMonths(6);
        }
        if ("one_year".equals(time)) {
            //返回当前时间加一年
            dateTime = dateTime.plusYears(1);
        }
        if ("two_years".equals(time)) {
            //返回当前时间加两年
            dateTime = dateTime.plusYears(2);
        }
        if (StringUtils.isNotBlank(time) && time.matches("\\d+")) {
            dateTime = dateTime.plusDays(Long.valueOf(time));
        }
        return dateTime;
    }

    /**
     * 根据当前时间取整数5分钟，10分钟， 15分钟......
     * @return
     */
    public static String toRoundedMinuteCurrentHourMinute() {
        LocalDateTime now = LocalDateTime.now();
        int roundedMinute = ((now.getMinute()) / 5) * 5;
        LocalDateTime roundedTime = now.with(LocalTime.of(now.getHour(), roundedMinute, 0));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_YMDHM);
        return  roundedTime.format(formatter);
    }

    /**
     * 获取上一个小时的时间并取整数5分钟，10分钟， 15分钟......
     * @return
     */
    public static String toRoundedMinutePreHourMinute() {
        LocalDateTime now = LocalDateTime.now();
        int hour = now.getHour();

        int roundedMinute = ((now.getMinute()) / 5) * 5;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_YMDHM);
        // 转换为字符串
        LocalDateTime lastHour = now.minus(1, ChronoUnit.HOURS);
        if(hour == 0){
            lastHour = now.with(LocalTime.of(23, roundedMinute));
        }
        LocalDateTime lastHoursRoundedTime = lastHour.with(LocalTime.of(lastHour.getHour(), roundedMinute, 0));
        return lastHoursRoundedTime.format(formatter);
    }

    /**
     * 小于半点时间00 大于半点30
     * @param pattern
     * @return
     */
    public static String currentHalf(String pattern) {
        LocalDateTime now = LocalDateTime.now();
        String localDateHour = DateUtils.toString(now, pattern);
        return now.getMinute() > 30 ? localDateHour+ "30":localDateHour+ "00";
    }


    /**
     * 小于半点时间00 大于半点30
     * @param pattern
     * @return
     */
    public static String beforeCurrentHalf(int beanHour) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime fourHoursAgo = now.minus(beanHour, ChronoUnit.HOURS);
        String minute = fourHoursAgo.getMinute() < 30 ?"00": "30";
        String current = DateUtils.current("yyyyMMddHH")+minute;
        return DateUtils.toString(fourHoursAgo, "yyyyMMddHH")+minute;
    }


}
