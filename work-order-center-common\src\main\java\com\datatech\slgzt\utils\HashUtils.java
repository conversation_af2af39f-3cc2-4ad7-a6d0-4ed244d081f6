package com.datatech.slgzt.utils;

import lombok.Data;

import java.security.MessageDigest;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月15日 13:02:55
 */
@Data
public class HashUtils {

    public static String calculateUniqueId(List<Long> list) {
        // 排序以确保顺序无关性
        Collections.sort(list);
        String joined = String.join(",", list.toString());

        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(joined.getBytes());
            return bytesToHex(hashBytes); // 转换为十六进制字符串
        } catch (Exception e) {
            throw new RuntimeException("哈希计算失败", e);
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }



}
