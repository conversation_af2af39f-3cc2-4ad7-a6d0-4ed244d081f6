package com.datatech.slgzt.utils;

import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2024年 12月21日 20:58:54
 */
public class QueryParamCheckUtil {

    /**
     * 查询参数是否包含‘%’
     *
     * @param o
     * @return
     * @throws Exception
     */
    public static boolean containsPercentage(Object o) {
        try {
            Field[] fields = o.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object filedValue = field.get(o);
                if (null != filedValue) {
                    if (StringUtils.contains(String.valueOf(filedValue), "%")) {
                        return true;
                    }
                }
            }
        } catch (IllegalAccessException e) {

        }
        return false;
    }
}
