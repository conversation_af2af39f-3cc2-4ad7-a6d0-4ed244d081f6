package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月06日 19:34:55
 */
@Data
public class RedisModel extends BaseProductModel {




    private String productName;

    /**
     * 云类型
     */
    private String catalogueDomainCode;

    /**
     * 云类型名称
     */
    private String catalogueDomainName;

    /**
     * 云平台id
     */
    private String domainCode;

    /**
     * 云平台名称
     */
    private String domainName;


    /**
     * 系统版本
     */
    private String imageVersion;

    /**
     * 系统名称
     */
    private String imageOs;

    /**
     * 系统版本镜像id
     * 通过数据库查询获得
     * 33297352f9834252bb91db2a2d0502b3
     */
    private String imageId;


    /**
     * 规格编码
     * 通过服务表获取类似ecs.c1.medium.4
     * 底层对应flavorCode
     */
    private String flavorCode;

    /**
     * 规格id
     */
    private String flavorId;
    /**
     * 规格名称
     * 通过服务表获取类似ecs.c1.medium.4
     * 底层对应flavorName
     */
    ;
    private String flavorName;

    /**
     * 规格类型:使用CATEGORY_NAME字段
     */
    private String flavorType;



    /**
     * 系统盘类型
     */
    private String sysDiskType;

    /**
     * 系统盘大小
     */
    private Integer sysDiskSize;


    /**
     * 是否挂载数据盘
     */
    private Boolean mountDataDisk;

    /**
     * 如果mountDataDisk为true，则需要填写挂载数据盘的列表
     * 挂载数据盘的列表
     */
    private List<EvsModel> mountDataDiskList;


    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 开通数量
     */
    private Integer openNum;

    /**
     * 绑定的ip地址
     * 冗余字段，用于展示
     */
    private String bindIp;


    /**
     * redis名称
     */
    private String redisName;

    /**
     * redis版本
     */
    private String redisVersion;

    /**
     * redis类型
     * 标准版之类的
     */
    private String redisType;


    /**
     * 是否绑定公网IP
     */
    private Boolean bindPublicIp;

    /**
     * 如果bindPublicIp为true，则需要填写绑定公网IP的列表
     * 绑定公网IP的列表
     */
    private List<EipModel> eipModelList;


    private List<PlaneNetworkModel> planeNetworkModel;

}
