package com.datatech.slgzt.model.usercenter;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: liu<PERSON><PERSON><PERSON>
 * @Date: 2025/1/7
 */

@Data
public class UserCenterUserDTO implements Serializable {

    /**
     * 用户id
     */
    private Long id;

    /**
     * 创建者id
     */
    private Long createdBy;

    /**
     * 修改者id
     */
    private Long updatedBy;

    /**
     * 是否有效 0 无效 1 有效
     */
    private Integer status;

    /**
     * 登录名称
     */
    @JSONField(name = "account")
    private String loginName;

    /**
     * 用户名称
     */
    @JSONField(name = "username")
    private String userName;

    /**
     * 电话
     */
    @JSONField(name = "mobilephone")
    private String phone;

    private String accountType;

    /**
     * 邮箱
     */
    @JSONField(name = "email")
    private String userEmail;

    /**
     * 组织id
     */
    private Long orgId;
    private String orgName;

    /**
     * 租户id，多个时逗号拼接
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 部门名称
     */
    private String departmentDn;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    private LocalDateTime updatedTime;

    /**
     * 角色列表
     */
    @JSONField(name = "roles")
    private List<UserCenterRoleDTO> oacRoles;

    /**
     * 租户列表
     */
    private List<UserCenterTenantDTO> tenants;

    /**
     * 是否运营组
     */
    private boolean operation = false;



    //userOrg
    private String userOrgName;

    //userOrgId
    private String pwd;

    private String sysDeptName;



}

