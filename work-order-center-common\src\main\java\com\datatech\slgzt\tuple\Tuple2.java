package com.datatech.slgzt.tuple;

import java.io.Serializable;

public class Tuple2<A, B> implements Serializable {
    public final A a;
    public final B b;

    public Tuple2(A a, B b) {
        this.a = a;
        this.b = b;
    }
    public static <A, B> Tuple2<A, B> of(A a, B b) {
        return new Tuple2<>(a, b);
    }

    @Override
    public String toString() {
        return "(" + a + ", " + b + ")";
    }
} 