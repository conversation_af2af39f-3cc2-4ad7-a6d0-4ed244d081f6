package com.datatech.slgzt.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 生成编码工具类
 */
public class CodeUtil {

    /**
     * 根据当前年月日，以及随机生成4个数，组成订单编码
     * 操作订单：O[Q/C]yyyyMMddHHmmss[4位随机]
     * 商品订单：G[Q/C]yyyyMMddHHmmss[4位随机]
     * 产品订单：P[Q/C]yyyyMMddHHmmss[4位随机]
     * 服务订单：S[Q/C]yyyyMMddHHmmss[4位随机]
     * @return
     */
    public static String getOrderCode(String prefix) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateNowStr = sdf.format(new Date());
        return prefix + dateNowStr + getRandom(4);
    }

    /**
     * 获取N位随机数
     *
     * @param n
     * @return
     */
    private static long getRandom(long n) {
        long min = 1, max = 9;
        for (int i = 1; i < n; i++) {
            min *= 10;
            max *= 10;
        }
        long rangeLong = (((long) (new Random().nextDouble() * (max - min)))) + min;
        return rangeLong;
    }

    /**
     * 根据产品类型生成实例编码-跟gid保持一致
     * @param productType
     * @return
     */
    public static String getInstanceCode(String productType) {
        return productType + "_" + System.currentTimeMillis() + Math.round((Math.random() + 1) * 1000);
    }
}
