package com.datatech.slgzt.service.corporate;

import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.opm.DgRecoveryOrderCreateOpm;
import com.datatech.slgzt.model.query.DgRecoveryOrderQuery;
import com.datatech.slgzt.utils.PageResult;

public interface DgRecoveryOrderService {


    /**
     * createRecoveryWorkOrder
     */
    String createRecoveryWorkOrder(DgRecoveryOrderCreateOpm opm);

    PageResult<DgRecoveryOrderDTO> page(DgRecoveryOrderQuery query, Long currentUserId);

    /**
     * 填充创建工单的校验信息
     * 主要是设备和网络直接的校验
     *
     * @param opm
     */
    void fillCheckCreate(DgRecoveryOrderCreateOpm opm);

    void recovery(String orderId);
}
