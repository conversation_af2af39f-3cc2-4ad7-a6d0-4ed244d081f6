package com.datatech.slgzt.model.user;

import lombok.Data;

import java.util.List;

/**
 * 权限表 --实体类
 *
 * <AUTHOR>
 * @date 2024/11/18 16:42
 **/
@Data
public class OacAuthority {


    /**
     * 权限编码
     */
    private String authCode;

    /**
     * 权限名称
     */
    private String authName;

    /**
     * 权限分类: menu: 菜单权限 element: 菜单元素权限 data: 数据权限 interface: 接口权限
     */
    private String authType;

    /**
     * 角色拥有的菜单
     */
    private List<OacMenu> oacMenus;
}