package com.datatech.slgzt.model.order;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增订单购物车草稿箱实体类
 */
@Data
public class UpsetOrderShoppingCartDTO implements Serializable {

    /**
     * 购物车草稿箱记录id
     */
    private Long id;

    /**
     * 创建者id
     */
    private Long createdBy;

    /**
     * 商品类型
     */
    @NotEmpty(message = "商品类型不能为空")
    private String goodsType;

    /**
     * 存储数据类型 0 购物车 1 草稿
     */
    @NotNull(message = "存储数据类型不能为空")
    @Min(value = 0, message = "最小值为0")
    @Max(value = 1, message = "最大值为1")
    private Integer dataType = 0;

    /**
     * 工单相关json格式数据
     */
    @NotNull(message = "工单相关商品信息不能为空")
    private Object orderJson;

}
