package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月03日 18:27:05
 */
@Data
@Accessors(chain = true)
public class DgRecoveryOrderQuery {

    private Integer pageNum;

    private Integer pageSize;

    private String orderCode;



    private LocalDateTime createTimeStart;

    private LocalDateTime createTimeEnd;











}
