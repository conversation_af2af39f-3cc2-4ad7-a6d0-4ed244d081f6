package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 20:26:55
 */
@Data
public class SlbModel extends BaseProductModel {


    private String slbName;


    private String flavorCode;

    private String flavorId;

    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 是否绑定公网IP
     */
    private Boolean bindPublicIp;

    /**
     * 如果bindPublicIp为true，则需要填写绑定公网IP的列表
     * 绑定公网IP的列表
     */
    private List<EipModel> EipModelList;

    /**
     * domainCode;
     */
    private String domainCode;

    /**
     * 规格类型:使用CATEGORY_NAME字段
     */
    private String flavorType;

    /**
     * 规格名称
     */
    private String flavorName;

    private String time;

    /**
     * 开通数量
     */
    private Integer openNum;

    private PlaneNetworkModel planeNetworkModel;

    /**
     * 云类型
     */
    private String catalogueDomainCode;





}
