package com.datatech.slgzt.utils;

import cn.hutool.core.util.StrUtil;
import com.datatech.slgzt.exception.BusinessException;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import java.util.Collection;

/**
 * 断言工具
 *
 * <AUTHOR>
 */
public class AssertUtil {

    /**
     * Assert.notNull(Object object, "object is required") - 对象非空
     *
     * @param object
     * @param message
     */
    public static void notNull(@Nullable Object object, String message) {
        if (object == null) {
            throw new BusinessException(message);
        }
    }

    /**
     * 字符串非空
     *
     * @param object
     * @param message
     */
    public static void notBlankString(@Nullable String object, String message) {
        if (StrUtil.isEmpty(object)) {
            throw new BusinessException(message);
        }
    }

    /**
     * Assert.notNull(Collection<?> collection, "collection is required") - collection非空
     *
     * @param collection
     * @param message
     */
    public static void notEmpty(@Nullable Collection<?> collection, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(message);
        }
    }

    /**
     * Assert.isTrue(boolean object, "object must be true") - 条件必须为true
     *
     * @param object
     * @param message
     */
    public static void isTrue(@Nullable boolean object, String message) {
        if (!object) {
            throw new BusinessException(message);
        }
    }

}