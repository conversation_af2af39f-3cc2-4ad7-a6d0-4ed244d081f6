package com.datatech.slgzt.impl.service.order;

import com.datatech.slgzt.enums.OrderLogStatusEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.WorkOrderAuthLogManager;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.query.StandardWorkOrderQuery;
import com.datatech.slgzt.model.query.WorkOrderAuthLogQuery;
import com.datatech.slgzt.service.order.WocOrderDataExportService;
import com.datatech.slgzt.service.standard.StandardWorkOrderService;
import com.datatech.slgzt.utils.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Service
public class WocOrderDataExportServiceImpl implements WocOrderDataExportService {


    @Resource
    private StandardWorkOrderService standardWorkOrderService;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;


    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    @Override
    public void exportExcel(HttpServletResponse response, StandardWorkOrderQuery query) throws Exception {
        query.setPageNum(1);
        query.setPageSize(Integer.MAX_VALUE);
        PageResult<StandardWorkOrderDTO> page = standardWorkOrderService.page(query, UserHelper.INSTANCE.getCurrentUserId());
        List<StandardWorkOrderDTO> list = page.getRecords();

        Workbook workbook = new XSSFWorkbook();
        // 2.创建一个工作表
        Sheet sheet = workbook.createSheet("工单数据");
        // 创建单元格样式
        CellStyle cellStyle = workbook.createCellStyle();
        // 水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 行0, 列0-9
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 11));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 12, 14));

        Row rowTitle = sheet.createRow(0);
        Cell cell0 = rowTitle.createCell(0);
        cell0.setCellStyle(cellStyle);
        cell0.setCellValue("工单信息");

        Cell cell10 = rowTitle.createCell(12);
        cell10.setCellStyle(cellStyle);
        cell10.setCellValue("厂家信息");
        // 3.创建行。第一行
        Row rowHeader = sheet.createRow(1);
        // 4.创建列。
        // (1,1) 第一行第一列的单元格
        rowHeader.createCell(0).setCellValue("工单标题");
        rowHeader.createCell(1).setCellValue("工单编号");
        rowHeader.createCell(2).setCellValue("云平台");
        rowHeader.createCell(3).setCellValue("业务系统");
        rowHeader.createCell(4).setCellValue("工单类型");
        rowHeader.createCell(5).setCellValue("所属业务模块");
        rowHeader.createCell(6).setCellValue("申请人");
        rowHeader.createCell(7).setCellValue("创建时间");
        rowHeader.createCell(8).setCellValue("到达节点时间");
        rowHeader.createCell(9).setCellValue("工单状态");
        rowHeader.createCell(10).setCellValue("工单闭环时间");
        rowHeader.createCell(11).setCellValue("资源申请说明");
        rowHeader.createCell(12).setCellValue("厂家");
        rowHeader.createCell(13).setCellValue("厂家负责人");
        rowHeader.createCell(14).setCellValue("厂家电话");
        int logLength = 0;

        for (int i = 0; i < list.size(); i++) {
            StandardWorkOrderDTO standardWorkOrderDTO = list.get(i);
            Row row = sheet.createRow(i + 2);
            row.createCell(0).setCellValue(standardWorkOrderDTO.getOrderTitle());
            row.createCell(1).setCellValue(standardWorkOrderDTO.getOrderCode());
            row.createCell(2).setCellValue(standardWorkOrderDTO.getDomainName());
            row.createCell(3).setCellValue(standardWorkOrderDTO.getBusinessSystemName());
            // todo 现在没有工单类型，暂时写死开通，后续回收时修改
            row.createCell(4).setCellValue("开通资源");
            row.createCell(5).setCellValue(standardWorkOrderDTO.getModuleName());
            row.createCell(6).setCellValue(standardWorkOrderDTO.getCreatedUserName());
            row.createCell(7).setCellValue(standardWorkOrderDTO.getCreateTime().format(formatter));
            row.createCell(8).setCellValue(standardWorkOrderDTO.getCurrentNodeStartTime().format(formatter));
            String status = "";
            if (StringUtils.isNotEmpty(standardWorkOrderDTO.getOrderStatus())){
               status = OrderLogStatusEnum.getByCode(standardWorkOrderDTO.getOrderStatus()).getDesc();
            }
            row.createCell(9).setCellValue(status);

            row.createCell(10).setCellValue(standardWorkOrderDTO.getWorkOrderEndTime().format(formatter));

            row.createCell(11).setCellValue(standardWorkOrderDTO.getOrderDesc());
            row.createCell(12).setCellValue(standardWorkOrderDTO.getManufacturer());
            row.createCell(13).setCellValue(standardWorkOrderDTO.getManufacturerContacts());
            row.createCell(14).setCellValue(standardWorkOrderDTO.getManufacturerMobile());
            List<WorkOrderAuthLogDTO> orderAuthLogVOList = workOrderAuthLogManager.list(new WorkOrderAuthLogQuery().setWorkOrderId(standardWorkOrderDTO.getId()));
            logLength = Math.max(logLength, orderAuthLogVOList.size());
            for (int j = 0; j < orderAuthLogVOList.size(); j++) {
                WorkOrderAuthLogDTO workOrderAuthLog = orderAuthLogVOList.get(j);
                row.createCell(15 + j * 4).setCellValue(workOrderAuthLog.getAuditNodeName());
                row.createCell(16 + j * 4).setCellValue(workOrderAuthLog.getAdvice());
                row.createCell(17 + j * 4).setCellValue(workOrderAuthLog.getUserName());
                row.createCell(18 + j * 4).setCellValue(workOrderAuthLog.getCreateTime().format(formatter));
            }
        }
        for (int i = 0; i < logLength; i++) {
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 15 + i * 4, 18 + i * 4));
            Cell cellLogs = rowTitle.createCell(15 + i * 4);
            cellLogs.setCellStyle(cellStyle);
            cellLogs.setCellValue("审批日志：" + (i + 1));
            rowHeader.createCell(15 + i * 4).setCellValue("审批节点");
            rowHeader.createCell(16 + i * 4).setCellValue("审批状态");
            rowHeader.createCell(17 + i * 4).setCellValue("审批人");
            rowHeader.createCell(18 + i * 4).setCellValue("审批时间");
        }
        for (int i = 0; i <= 14; i++) {
            sheet.setColumnWidth(i, 256 * 17);
        }
        for (int i = 15; i <= 14 + logLength * 4; i+=4) {
            sheet.setColumnWidth(i, 256 * 17);
            sheet.setColumnWidth(i + 1, 256 * 14);
            sheet.setColumnWidth(i + 2, 256 * 11);
            sheet.setColumnWidth(i + 3, 256 * 18);
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("工单审批", "UTF-8"));
        // 将workbook内容写入到响应流
        workbook.write(response.getOutputStream());
    }
}
