package com.datatech.slgzt.model.nostander;

import lombok.Data;

import java.util.List;

@Data
public class NetworkModel extends PNatModel{

    /**
     * {
     * 	"vpcInfo": {
     * 		"networkIds": ["6bb652d97f3e4324bd0c4056ab3a094c"],
     * 		"subnets": [{
     * 			"subnetId": "sub_17410593331441354"
     * 		        }]    * 	}
     * }
     */

    /**
     * 网络平面
     */
    private String plane;

    /**
     * 网络id
     */
    private List<String> networkIds;

    private List<SubnetModel> subnets;

    /**
     * 排序（按照从小到大顺序塞参数到资源开通请求参数中）
     */
    private Integer sort;

    @Data
    public static class SubnetModel {
        /**
         * 子网id
         */
        private String subnetId;

        /**
         * 子网ip
         */
        private String subnetIp;

        /**
         * 子网名称
         */
        private String subnetName;

        /**
         * 指定ip
         */
        private String ipAddress;

    }


}