package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.order.StandardWorkOrderProductDO;
import com.datatech.slgzt.model.order.WocResourceOrderProductRelDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface WocResourceOrderProductRelManagerConvert {

    WocResourceOrderProductRelDTO do2DTO(StandardWorkOrderProductDO relDO);

    StandardWorkOrderProductDO dto2DO(WocResourceOrderProductRelDTO relDTO);
}
