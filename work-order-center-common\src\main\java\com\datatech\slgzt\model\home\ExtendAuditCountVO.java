package com.datatech.slgzt.model.home;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/31
 */

@Data
@Accessors(chain = true)
public class ExtendAuditCountVO {

    /**
     * 资源开通数统计
     */
    private AuditCountVo workOrder;

    /**
     * 资源回收数统计
     */
    private AuditCountVo recycleOrder;

    /**
     * 变更数统计
     */
    private AuditCountVo changeOrder;

    /**
     * 非标工单数统计
     */
    private AuditCountVo nonStanderOrder;

}

