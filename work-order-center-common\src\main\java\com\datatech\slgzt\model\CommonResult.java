package com.datatech.slgzt.model;

import com.datatech.slgzt.utils.TaceIdContext;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 返回对象
 */
@Data
@AllArgsConstructor
public class CommonResult<T> {
    /**
     * 0失败；1成功
     */
    private Integer success = 1;

    /**
     * 响应码: 200正常
     */
    private Integer code;

    /**
     * 返回信息
     */
    private String message = "";

    /**
     * 返回实体
     */
    private T entity;

    public String getTraceId() {
        return TaceIdContext.generateTraceIdGet();
    }

    public static <U> CommonResult build(Integer success, Integer code, String msg, U data) {
        return new CommonResult(success, code, msg, data);
    }

    public static <U> CommonResult success() {
        return build(1, 200, "success", "");
    }

    public static <U> CommonResult success(String message, final U data) {
        return build(1, 200, message, data);
    }

    public static <U> CommonResult<U> success(final U data) {
        return new CommonResult<U>(1, 200, "", data);
    }

    public static CommonResult failure(final String message) {
        return new CommonResult(0, 400, message, "");
    }

    public static CommonResult failure(final Integer code, final String message) {
        return new CommonResult(0, code, message, "");
    }
}
