package com.datatech.slgzt.component;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.security.MessageDigest;

@Slf4j
@Component
@RefreshScope
public class OpenFeignInterceptor implements RequestInterceptor {

    /**
     * CMDB分配的SK秘钥
     */
    @Value("${cmdb.secretKey}")
    private String secretKey;

    /**
     * CMDB分配的AK客户端标识
     */
    @Value("${cmdb.accessKey}")
    private String accessKey;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String urlEncode = requestTemplate.url();
        String url;
        log.info("请求url----" + urlEncode);
        try {
            url = URLDecoder.decode(urlEncode, "utf-8");
            log.info("解码后URL---" + url);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("路径编码错误:" + urlEncode);
        }

        // 签名
        if (!url.contains("signature") && url.startsWith("/cmdb")) {

            // 请求方法,POST/GET等
            String method = requestTemplate.method();

            // 请求资源路径,/api/xxx
            String path = requestTemplate.path();

            // 请求参数/api/?xxx=xxx
            String param = "";

            // 请求类型
            String contentType = "application/json";

            // 请求Data的MD5值
            String contentMd5 = null;
            try {
                contentMd5 = md5(new String(requestTemplate.body(), "utf-8"), "UTF-8").toLowerCase();
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            // 请求时间戳,精确到秒
            String requestTime = System.currentTimeMillis() / 1000 + "";

            // 签名字符串
            String stringToSign =
                    method + "\n"
                            + path + "\n"
                            + param + "\n"
                            + contentType + "\n"
                            + contentMd5 + "\n"
                            + requestTime + "\n"
                            + accessKey;

            // 计算签名
            String sign = this.hmacSha1(stringToSign, secretKey);

            // 签名结果拼接
            String newUrl =
                    new StringBuilder()
                            .append(url)
                            .append("?accesskey=" + accessKey)
                            .append("&signature=" + sign)
                            .append("&expires=" + requestTime)
                            .toString();
            requestTemplate.uri(newUrl);
            requestTemplate.header("Content-Type","application/json");
            requestTemplate.header("Host", "openapi.easyops-only.com");

            log.info("签名结果---" + newUrl);
        }

    }


    /**
     * hmacSha1算法加密
     */
    private String hmacSha1(String src, String key) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(key.getBytes("utf-8"), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(src.getBytes("utf-8"));
            return Hex.encodeHexString(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 转MD5 防止中文乱码问题
     *
     * @param s            指定字符串
     * @param encodingType 指定编码
     * @return String
     */
    private static String md5(String s, String encodingType) {
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

        try {
            // 按照相应编码格式获取byte[]
            byte[] btInput = s.getBytes(encodingType);
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式

            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;

            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            return "-1";
        }
    }


}
