package com.datatech.slgzt.model.usercenter;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/1/7
 */

@Data
public class UserCenterTenantDTO implements Serializable {

    /**
     * 租户id
     */
    private Long id;

    /**
     * 创建者id
     */
    private Long createdBy;

    /**
     * 修改者id
     */
    private Long updatedBy;

    /**
     * 是否有效 0 无效 1 有效
     */
    private Integer status;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    @JSONField(name = "name")
    private String tenantName;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 用户中心租户id
     */
    private String cmpTenantId;

    /**
     * 计费号
     */
    private String billId;

    private String customNo;

    /**
     * 租户类型（0：内部租户；1：外部租户）
     */
    private Long tenantType;

    /**
     * 租户所属人id
     */
    @JSONField(name = "ownerId")
    private Long tenantOwnerId;

    /**
     * 租户所属人名称
     */
    @JSONField(name = "ownerName")
    private String tenantOwnerName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    private LocalDateTime updatedTime;
}

