package com.datatech.slgzt.model.change;

import com.datatech.slgzt.enums.ProductTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月30日 12:34:04
 */
@Data
public class ChangeBaseModel {

    private String createWorkOrderId;

    /**
     * 产品订单id
     */
    private Long productOrderId;

    /**
     * 产品类型
     *
     * @see ProductTypeEnum
     */
    private String productType;

    //资源池id
    private String regionId;
    //资源池名称
    private String regionName;
    //资源池Code
    private String regionCode;

    //AzId
    private String azId;

    //Az名称
    private String azName;

    //AzCode
    private String azCode;

    //计费号
    private String billId;

    //变更类型
    private List<String> changeType;

    //更变规格名称
    private String changeFlavorName;

    //更变规格id
    private String changeFlavorId;

    //更变规格类型
    private String changeFlavorType;

    //更变的存储容量 更变产品类型是EVS的就会有
    private Integer changeVolumeSize;

    //更变带宽
    private Integer changeBandwidth;

    //更变延长时间
    private String changeTime;

    //变更前的到期时间
    private LocalDateTime expireTime;

    //变更前的到期时间+变更时长
    private LocalDateTime newExpireTime;


    private Long resourceDetailId;


    private Long tenantId;

    private String tenantName;

    private String businessSystemCode;

    private Long businessSystemId;


    private String customNo;

    private Boolean tenantConfirm;

    private Long id;

    private String changeStatus;

    private String message;

    private Boolean blockWarning;

    private String deviceStatus;

    private Boolean canClose;

    //交维状态
    private String handoverStatus;

    private String domainName;

    private String domainCode;

    private String resourceId;

    private String eip;
    private String spec;
    private String bandwidth;
}
