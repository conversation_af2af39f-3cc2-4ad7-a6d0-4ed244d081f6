package com.datatech.slgzt.model.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 新增订单购物车草稿箱实体类
 */
@Data
public class UpsetOrderShoppingCart implements Serializable {

    /**
     * 购物车草稿箱记录id
     */
    private Long id;

    /**
     * 创建者id
     */
    private Long createdBy;

    /**
     * 商品类型
     */
    @NotBlank(message = "商品类型不能为空")
    private String goodType;

    /**
     * 商品类型
     */
    private String goodsType;

    /**
     * 存储数据类型 0 购物车 1 草稿
     */
    @NotBlank(message = "存储数据类型不能为空")
    @Min(value = 0, message = "最小值为0")
    @Max(value = 1, message = "最大值为1")
    private Integer dataType = 0;

    /**
     * 工单相关json格式数据
     */
    @Deprecated
    private transient byte[] orderJsonBytes;

    @NotBlank(message = "工单相关商品信息不能为空")
    private String orderJson;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

}
