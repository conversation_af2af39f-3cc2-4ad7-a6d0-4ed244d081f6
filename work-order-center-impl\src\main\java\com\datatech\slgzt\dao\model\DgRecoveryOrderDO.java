package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 14:37:04
 */
@Data
@TableName("WOC_DG_RECOVERY_ORDER")
public class DgRecoveryOrderDO {

    @TableField("ID")
    private String id;
    // 订单编号
    @TableField("ORDER_CODE")
    private String orderCode;

    //租户id
    @TableField("TENANT_ID")
    private Long tenantId;

    //租户名称
    @TableField("TENANT_NAME")
    private String tenantName;

    //创建人
    @TableField("CREATOR")
    private String creator;

    //创建时间
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    //创建人id
    @TableField("CREATOR_ID")
    private Long creatorId;

    @TableField("JOB_EXECUTION_ID")
    private Long jobExecutionId;
}
