package com.datatech.slgzt.consumer;

import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.SMSMessageTemplateEnum;
import com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.model.sms.SmsSendModel;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.UserService;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月17日 19:45:07
 */
@Slf4j
@Component
public class SmsSendConsumer {


    @Value("${http.userCenterUrl}")
    private String userCenterUrl;

    @Resource
    private UserService userService;

    private final static String WORK_ORDER_SMS_SEND = "woc_activity_sms_send_topic";


    @KafkaListener(groupId = "work-order-sms-send-group", topics = {WORK_ORDER_SMS_SEND})
    public void consumeMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
        log.info("短信监听任务消息: {}", consumerRecordList.size());
        for (ConsumerRecord<String, String> record : consumerRecordList) {
            SmsSendModel sendModel = JSONObject.parseObject(record.value(), SmsSendModel.class);
            String orderType = sendModel.getOrderType();
            String formatText = "";
            switch (orderType) {
                case "subscribe":
                    formatText = fillSubscribeText(sendModel);
                    break;
                case "nonStandardSubscribe":
                    formatText = fillNonStandardSubscribeText(sendModel);
                    break;
                case "recovery":
                    formatText = fillRecoveryText(sendModel);
                    break;
                case "change":
                    formatText = fillChangeText(sendModel);
                    break;
                case "due":
                    formatText = fillDueOrderText(sendModel);
                    break;
                case "expire":
                    formatText = fillExpireOrderText(sendModel);
                    break;
                case "change_success":
                    formatText = fillChangeRecoverySuccess(sendModel);
                    break;
                case "change_fail":
                    formatText = fillChangeRecoveryFail(sendModel);
                    break;
                case "autoRecovery":
                    formatText = fillAutoRecoveryOrderText(sendModel);
                    break;
                case "tenant_confirm_reminder":
                    formatText = fillTenantConfirmReminderText(sendModel, false);
                    break;
                case "tenant_confirm_reminder_resource":
                    formatText = fillTenantConfirmReminderText(sendModel, true);
                    break;
                case "dg_fail":
                    formatText = fillDgFailText(sendModel);
                    break;
                default:
                    formatText = fillSubscribeText(sendModel);
                    break;
            }
            sendSms(sendModel, formatText);
        }
    }

    private String fillChangeText(SmsSendModel sendModel) {
        String formatText = "";
        //如果当前节点是空的，那就是完成任务
        if (sendModel.getCurrentNode() == null || ActivitiStatusEnum.AUTODIT_END.getNode().equals(sendModel.getCurrentNode())) {
            formatText = String.format(SMSMessageTemplateEnum.CHANGE_COMPLETE.message(), sendModel.getOrderCode());
        } else if (ActivitiStatusEnum.SHUTDOWN.getNode().equals(sendModel.getCurrentNode())) {
            formatText = String.format(SMSMessageTemplateEnum.CHANGE_PASS_SHUTDOWN.message(), sendModel.getOrderCode());
        } else {
            ActivityEnum.ActivityStatusEnum activityStatus = sendModel.getActivityStatus();
            if (activityStatus == ActivityEnum.ActivityStatusEnum.PASS) {
                formatText = String.format(SMSMessageTemplateEnum.CHANGE_PASS.message(), sendModel.getOrderCode(), sendModel.getCurrentNodeName());
            } else if (activityStatus == ActivityEnum.ActivityStatusEnum.REJECT) {
                formatText = String.format(SMSMessageTemplateEnum.CHANGE_REJECT.message(), sendModel.getOrderCode(), sendModel.getCurrentNodeName());
            }
        }

        return formatText;
    }

    private String fillChangeRecoverySuccess(SmsSendModel sendModel) {
        return String.format(SMSMessageTemplateEnum.CHANGE_RESOURCE_CHANGE_SUCCESS.message(), sendModel.getOrderCode());
    }

    private String fillChangeRecoveryFail(SmsSendModel sendModel) {
        return String.format(SMSMessageTemplateEnum.CHANGE_RESOURCE_CHANGE_FAIL.message(), sendModel.getOrderCode());
    }

    private String fillSubscribeText(SmsSendModel sendModel) {
        String formatText = "";
        //如果当前节点是空的，那就是完成任务
        if (sendModel.getCurrentNode() == null || ActivitiStatusEnum.AUTODIT_END.getNode().equals(sendModel.getCurrentNode())) {
            formatText = String.format(SMSMessageTemplateEnum.OPEN_SUCCESS.message(), sendModel.getOrderCode());
        } else {
            ActivityEnum.ActivityStatusEnum activityStatus = sendModel.getActivityStatus();
            if (activityStatus == ActivityEnum.ActivityStatusEnum.PASS) {
                formatText = String.format(SMSMessageTemplateEnum.OPEN.message(), sendModel.getOrderCode(), sendModel.getCurrentNodeName());
            } else if (activityStatus == ActivityEnum.ActivityStatusEnum.REJECT) {
                formatText = String.format(SMSMessageTemplateEnum.REJECT.message(), sendModel.getOrderCode(), sendModel.getCurrentNodeName());
            }
        }

        return formatText;
    }

    private String fillNonStandardSubscribeText(SmsSendModel sendModel) {
        String formatText = "";
        //如果当前节点是空的，那就是完成任务
        if (sendModel.getCurrentNode() == null || ActivitiStatusEnum.AUTODIT_END.getNode().equals(sendModel.getCurrentNode())) {
            formatText = String.format(SMSMessageTemplateEnum.NON_STANDARD_COMPLETE.message(), sendModel.getOrderCode());
        } else {
            ActivityEnum.ActivityStatusEnum activityStatus = sendModel.getActivityStatus();
            if (activityStatus == ActivityEnum.ActivityStatusEnum.PASS) {
                formatText = String.format(SMSMessageTemplateEnum.NON_STANDARD_PASS.message(), sendModel.getOrderCode(), sendModel.getCurrentNodeName());
            } else if (activityStatus == ActivityEnum.ActivityStatusEnum.REJECT) {
                formatText = String.format(SMSMessageTemplateEnum.NON_STANDARD_REJECT.message(), sendModel.getOrderCode(), sendModel.getCurrentNodeName());
            }
        }

        return formatText;
    }

    private String fillRecoveryText(SmsSendModel sendModel) {
        String formatText = "";
        if (sendModel.getCurrentNode() == null || ActivitiStatusEnum.AUTODIT_END.getNode().equals(sendModel.getCurrentNode())) {
            formatText = String.format(SMSMessageTemplateEnum.RECYCLE_COMPLE.message(), sendModel.getOrderCode());
        } else {
            ActivityEnum.ActivityStatusEnum activityStatus = sendModel.getActivityStatus();
            if (activityStatus == ActivityEnum.ActivityStatusEnum.PASS) {
                formatText = String.format(SMSMessageTemplateEnum.RECYCLE_PASS.message(), sendModel.getOrderCode(), sendModel.getCurrentNodeName());
            } else if (activityStatus == ActivityEnum.ActivityStatusEnum.REJECT) {
                formatText = String.format(SMSMessageTemplateEnum.RECYCLE_REJECT.message(), sendModel.getOrderCode(), sendModel.getCurrentNodeName());
            }
        }

        return formatText;
    }
    private String fillDueOrderText(SmsSendModel sendModel) {
        return String.format(SMSMessageTemplateEnum.DUE_ORDER.message(), sendModel.getOrderCode(), sendModel.getExpireTime());
    }

    private String fillExpireOrderText(SmsSendModel sendModel) {
        return String.format(SMSMessageTemplateEnum.EXPIRE_ORDER.message(), sendModel.getOrderCode(), sendModel.getExpireTime());
    }

    private String fillAutoRecoveryOrderText(SmsSendModel sendModel) {
        return String.format(SMSMessageTemplateEnum.RECOVERY_ORDER.message(), sendModel.getOrderCode());
    }


    private String fillDgFailText(SmsSendModel sendModel) {
        return String.format(SMSMessageTemplateEnum.DG_FAIL.message(), sendModel.getOrderCode(), sendModel.getProductType());
    }


    /**
     * 填充租户确认提醒短信内容
     *
     * @param sendModel 短信发送模型
     * @return 格式化后的短信内容
     */
    private String fillTenantConfirmReminderText(SmsSendModel sendModel, boolean isResourceGroup) {
        // 使用工单编号和工单标题填充短信模板
        return String.format(isResourceGroup ?
                        SMSMessageTemplateEnum.TENANT_CONFIRM_REMINDER_RESOURCE.message()
                        : SMSMessageTemplateEnum.TENANT_CONFIRM_REMINDER.message(),
                sendModel.getOrderTypeCn(),
                sendModel.getOrderCode(),
                sendModel.getOrderTitle(),
                sendModel.getOrderTypeCn());
    }

    /**
     * 标准流程发短信
     */
    private void sendSms(SmsSendModel sendModel, String formatText) {
        log.info("sendSms start, formatText:{}, sendmodel:{}",formatText,sendModel);
        //判断用户是不是空 如果不为空就发送短信
        if (sendModel.getUserId() != null) {
            //查询用户
            UserCenterUserDTO user = userService.getUserById(sendModel.getUserId());
            if (user == null) {
                log.error("发送短信失败，用户不存在 用户Id:{}", sendModel.getUserId());
                return;
            }
            sendMessage(user.getPhone(), formatText);
        }
        //如果角色不为空
        if (!CollectionUtils.isEmpty(sendModel.getRoles())) {
            for (String role : sendModel.getRoles()) {
                //查询用户
                List<UserCenterUserDTO> userList = userService.getUserListByRoleCode(role);
                if (userList == null || userList.isEmpty()) {
                    log.error("发送短信失败，角色不存在 角色Id:{}", role);
                    return;
                }
                for (UserCenterUserDTO user : userList) {
                    sendMessage(user.getPhone(), formatText);
                }
            }
        }
        // 如果手机号不为空
        if (sendModel.getPhone() != null) {
            sendMessage(sendModel.getPhone(), formatText);
        }
    }


    public void sendMessage(String phone, String content) {
        Mapper mapper = OkHttps.sync(userCenterUrl + "/ccmp/usercenter/messageSend/sendMessage")
                .addUrlPara("phone", phone)
                .addUrlPara("content", content)
                .bodyType(OkHttps.JSON)
                .get()
                .getBody()
                .toMapper();

        Integer resultCode = mapper.getInt("code");
        if (resultCode == 500) {
            log.error("发送短信失败，手机号：{}，内容：{}", phone, content);
        }
    }
}
