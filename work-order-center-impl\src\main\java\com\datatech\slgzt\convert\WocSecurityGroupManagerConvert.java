package com.datatech.slgzt.convert;

import com.datatech.slgzt.dao.model.security.WocSecurityGroupDO;
import com.datatech.slgzt.model.dto.WocSecurityGroupDTO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-04-29 10:14
 **/
@Mapper(componentModel = "spring")
public interface WocSecurityGroupManagerConvert {

    WocSecurityGroupDTO do2DTO(WocSecurityGroupDO groupDO);

    List<WocSecurityGroupDTO> do2DTOs(List<WocSecurityGroupDO> groupDO);

    WocSecurityGroupDO dto2DO(WocSecurityGroupDTO groupDTO);
}
