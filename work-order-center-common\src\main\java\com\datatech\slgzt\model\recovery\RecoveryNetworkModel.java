package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:55:56
 */
@Data
public class RecoveryNetworkModel extends BaseReconveryProductModel {

    //networkId
    private String networkId;

    //网络名称
    @ExcelExportHeader(value = "网络名称")
    private String networkName;

    //云类型
    private String catalogueDomainCode;

    @ExcelExportHeader(value = "云类型")
    private String catalogueDomainName;

    @ExcelExportHeader(value = "工单编号")
    private String orderCode;

    @ExcelExportHeader(value = "网络类型")
    private String networkType;





}
