package com.datatech.slgzt.component;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.AuthorityCodeEnum;
import com.datatech.slgzt.enums.GlobalExceptionEnum;
import com.datatech.slgzt.enums.UserCenterRequestEnum;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.order.OrderBill;
import com.datatech.slgzt.model.user.OacUser;
import com.datatech.slgzt.model.user.UserRole;
import com.datatech.slgzt.model.usercenter.UserAppUpdateReqDTO;
import com.datatech.slgzt.model.usercenter.UserCenterRoleDTO;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.usercenter.UserSelectRequestDTO;
import com.datatech.slgzt.service.role.CmpRoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户体系调用用户中心管理服务
 *
 * @Author: liupeihan
 * @Date: 2025/1/7
 */
@Component
@Slf4j
public class UserCenterRequestServiceComponent {

    @Value("${http.userCenterUrl}")
    private String userCenterUrl;

    @Resource
    private HttpTemplateUtil httpTemplateUtil;

    @Resource
    private CmpRoleService cmpRoleService;

    public OacUser getUserDetail(UserSelectRequestDTO userSelectRequestDTO) {
        HashMap<String, String> requestMap = new HashMap<>();
        if (userSelectRequestDTO.getUserId() != null) {
            requestMap.put("id", String.valueOf(userSelectRequestDTO.getUserId()));
        }

        requestMap.put("account", userSelectRequestDTO.getAccount());
        String json = processGetRequest(requestMap, UserCenterRequestEnum.GET_USER_DETAIL.getUrl());
        //log.info("请求用户中心用户信息：{}", JSON.toJSONString(json));

        UserCenterUserDTO userCenterUserDTO = JSON.parseObject(json, UserCenterUserDTO.class);
        return BeanUtil.copyProperties(userCenterUserDTO, OacUser.class);
    }

    public List<UserRole> getRoleList(UserSelectRequestDTO userSelectRequestDTO) {
        HashMap<String, String> requestMap = new HashMap<>();
        if (userSelectRequestDTO.getUserId() != null) {
            requestMap.put("userId", String.valueOf(userSelectRequestDTO.getUserId()));
        }

        requestMap.put("roleCode", userSelectRequestDTO.getRoleCode());
        String json = processGetRequest(requestMap, UserCenterRequestEnum.GET_ROLE_LIST.getUrl());
        List<UserCenterRoleDTO> roleDTOS = JSONArray.parseArray(json, UserCenterRoleDTO.class);
        List<UserRole> roles = BeanUtil.copyToList(roleDTOS, UserRole.class);

        // 检查用户中心有关于租户和运营组角色，包含时则将租户确认以及网络开通、资源开通的角色添加进去
        processRoleData(roles);
        return roles;
    }

    private void processRoleData(List<UserRole> roles) {
        if (CollectionUtil.isNotEmpty(roles)) {
            List<String> roleCodes = roles.stream().map(UserRole::getCode).collect(Collectors.toList());
            if (roleCodes.contains(AuthorityCodeEnum.OPERATION_GROUP.code())) {
                if (!roleCodes.contains(AuthorityCodeEnum.RESOURCE_CREATION.code())) {
                    UserRole role = new UserRole();
                    role.setCode(AuthorityCodeEnum.RESOURCE_CREATION.code());
                    role.setName(AuthorityCodeEnum.RESOURCE_CREATION.name());
                    roles.add(role);
                }
                if (!roleCodes.contains(AuthorityCodeEnum.NETWORK_PROVISIONING.code())) {
                    UserRole role = new UserRole();
                    role.setCode(AuthorityCodeEnum.NETWORK_PROVISIONING.code());
                    role.setName(AuthorityCodeEnum.NETWORK_PROVISIONING.name());
                    roles.add(role);
                }
                // 添加回收相关的交维清退、资源回收和网络回收相关角色
                if (!roleCodes.contains(AuthorityCodeEnum.RETREAT_DIMENSION.code())) {
                    UserRole role = new UserRole();
                    role.setCode(AuthorityCodeEnum.RETREAT_DIMENSION.code());
                    role.setName(AuthorityCodeEnum.RETREAT_DIMENSION.name());
                    roles.add(role);
                }
                if (!roleCodes.contains(AuthorityCodeEnum.RESOURCE_RECOVERY.code())) {
                    UserRole role = new UserRole();
                    role.setCode(AuthorityCodeEnum.RESOURCE_RECOVERY.code());
                    role.setName(AuthorityCodeEnum.RESOURCE_RECOVERY.name());
                    roles.add(role);
                }
                if (!roleCodes.contains(AuthorityCodeEnum.NETWORK_RECOVERY.code())) {
                    UserRole role = new UserRole();
                    role.setCode(AuthorityCodeEnum.NETWORK_RECOVERY.code());
                    role.setName(AuthorityCodeEnum.NETWORK_RECOVERY.name());
                    roles.add(role);
                }
            }

            if (roleCodes.contains(AuthorityCodeEnum.USER_TASK.code())
                    && !roleCodes.contains(AuthorityCodeEnum.TENANT_TASK.code())) {
                UserRole role = new UserRole();
                role.setCode(AuthorityCodeEnum.TENANT_TASK.code());
                role.setName(AuthorityCodeEnum.TENANT_TASK.name());
                roles.add(role);
            }
            if (roleCodes.contains(AuthorityCodeEnum.TENANT_ADMIN.code()) || roleCodes.contains(AuthorityCodeEnum.FREE_USER.code())) {
                UserRole role = new UserRole();
                role.setCode(AuthorityCodeEnum.TENANT_TASK.code());
                role.setName(AuthorityCodeEnum.TENANT_TASK.name());
                roles.add(role);
                UserRole role2 = new UserRole();
                role2.setCode(AuthorityCodeEnum.USER_TASK.code());
                role2.setName(AuthorityCodeEnum.USER_TASK.name());
                roles.add(role2);
            }

            if (roleCodes.contains(AuthorityCodeEnum.SUPER_ADMIN.code())) {
                List<AuthorityCodeEnum> authorityCodeEnumList = Arrays.asList(AuthorityCodeEnum.USER_TASK, AuthorityCodeEnum.TENANT_TASK, AuthorityCodeEnum.SCHEMA_ADMINISTRATOR,
                        AuthorityCodeEnum.BUSINESS_DEPART_LEADER, AuthorityCodeEnum.BUSINESS_DEPART_LEADER2, AuthorityCodeEnum.BUSINESS2_DEPART_LEADER,
                        AuthorityCodeEnum.CLOUD_LEADER, AuthorityCodeEnum.CLOUD_LEADER_2, AuthorityCodeEnum.NETWORK_PROVISIONING, AuthorityCodeEnum.RESOURCE_CREATION);
                for (AuthorityCodeEnum codeEnum : authorityCodeEnumList) {
                    UserRole role = new UserRole();
                    role.setCode(codeEnum.code());
                    role.setName(codeEnum.name());
                    roles.add(role);
                }
            }
        }
    }

    private String processGetRequest(Map<String, String> requestMap, String url) {
        String urlParam = httpTemplateUtil.mapToQueryString(requestMap);
        urlParam = StringUtils.isEmpty(urlParam) ? "" : "?" + urlParam;

        HttpHeaders headers = new HttpHeaders();
        headers.set("RemoteUser", "BusinessCenter");
        JSONObject json = httpTemplateUtil.exchange(userCenterUrl + url + urlParam, HttpMethod.GET, new HttpEntity<>(headers), requestMap);
        Integer result = json.getInteger("success");
        if (result != 1) {
            String message = String.format("请求用户中心失败，请求路径:%s，请求参数:%s", url, urlParam);
            log.error(message);
            throw UniversalException.build(GlobalExceptionEnum.CALL_USER_CENTER_FAILURE.getCode(), message);
        }
        String entity = json.getString("entity");
        if (StringUtils.isBlank(entity)) {
            String message = String.format("请求用户中心获取的数据为空，请求路径:%s，请求参数:%s", url, urlParam);
            log.info(message);
            throw UniversalException.build(GlobalExceptionEnum.CALL_USER_CENTER_FAILURE.getCode(), message);
        }

        return entity;
    }

    public OrderBill getBillIdByCode(UserSelectRequestDTO userSelectRequestDTO) {
        if (userSelectRequestDTO.getSystemCode() == null) {
            log.error("业务系统编码不能为空,业务上云对接用户中心获取billId失败");
            return null;
        }

        HashMap<String, String> requestMap = new HashMap<>();
        String json = processGetRequest(requestMap, UserCenterRequestEnum.GET_BILL_ID_BY_SYSTEM_CODE.getUrl() + "/" + userSelectRequestDTO.getSystemCode());
        return JSON.parseObject(json, OrderBill.class);
    }

    public Boolean addUserRole(UserSelectRequestDTO extentOpenOrderDTO) {
        List<UserCenterRoleDTO> userCenterRoleDTOS = cmpRoleService.selectRoleByUserId(extentOpenOrderDTO.getUserId());
        List<UserCenterRoleDTO> roleDTOS = userCenterRoleDTOS.stream().filter(item -> item != null && StringUtils.isNotEmpty(item.getCode())).collect(Collectors.toList());
        List<UserCenterRoleDTO> collect = roleDTOS.stream().filter(item -> item.getCode().equals(extentOpenOrderDTO.getRoleCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect)) {
            return true;
        }

        UserCenterRoleDTO role = cmpRoleService.selectByRoleCode(extentOpenOrderDTO.getRoleCode());
        if (role == null) {
            log.error("用户中心角色编码:{}未初始化，请初始化角色数据", extentOpenOrderDTO.getRoleCode());
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("code", extentOpenOrderDTO.getRoleCode());
            AuthorityCodeEnum byCode = AuthorityCodeEnum.getByCode(extentOpenOrderDTO.getRoleCode());
            requestMap.put("name", byCode == null ? extentOpenOrderDTO.getRoleCode() : byCode.message());
            requestMap.put("description", byCode == null ? extentOpenOrderDTO.getRoleCode() : byCode.message());
            processPostRequest(requestMap, UserCenterRequestEnum.CREATE_ROLE_URL.getUrl());
        }

        role = cmpRoleService.selectByRoleCode(extentOpenOrderDTO.getRoleCode());
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("userIds", Collections.singletonList(extentOpenOrderDTO.getUserId()));
        requestMap.put("roleIds", Collections.singletonList(role.getId()));
        processPostRequest(requestMap, UserCenterRequestEnum.APPEND_USER_ROLE_URL.getUrl());
        return true;
    }

    private String processPostRequest(Map<String, Object> requestMap, String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("RemoteUser", "BusinessCenter");
        HttpEntity entity = new HttpEntity(requestMap, headers);
        JSONObject json = httpTemplateUtil.exchange(userCenterUrl + url, HttpMethod.POST, entity, new HashMap<>());
        Integer result = json.getInteger("success");
        if (result != 1) {
            String message = json.getString("message");
            log.info(message);
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), "对接用户中心失败,errorMessage:" + message);
        }

        return json.getString("entity");
    }

    public String updateUserAppMessage(UserAppUpdateReqDTO updateReqDTO, String url) {
        return processPutRequest(updateReqDTO, url);
    }

    private String processPutRequest(Object body, String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("RemoteUser", "BusinessCenter");
        HttpEntity entity = new HttpEntity(body, headers);
        JSONObject json = httpTemplateUtil.exchange(userCenterUrl + url, HttpMethod.PUT, entity, new HashMap<>());
        return json.getString("success");
    }
}

