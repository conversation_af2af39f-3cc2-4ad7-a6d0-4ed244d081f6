package com.datatech.slgzt.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class HttpTemplateUtil {

    @Resource
    private RestTemplate restTemplate;

    /**
     * 发起流程
     */
    public JSONObject exchange(String url, HttpMethod method, HttpEntity<?> requestEntity, Map<String, ?> uriVariables) {
        log.debug("request url:{}", url);
        log.debug("request jsonString:{}", JSON.toJSONString(requestEntity));
        ResponseEntity<String> exchange = restTemplate.exchange(url, method, requestEntity, String.class, uriVariables);
        JSONObject json = JSONObject.parseObject(exchange.getBody());
        log.debug("activite response result:{}", exchange.getBody());
        return json;
    }

    /**
     * map转GET 请求参数字符串
     *
     * @param requestMap 请求参数
     * @return ?name=1&city=2
     */
    public String mapToQueryString(Map<String, String> requestMap) {
        if (requestMap == null || requestMap.isEmpty()) {
            return "";
        }

        return requestMap.entrySet().stream()
                .map(entry -> {
                    try {
                        String key = URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8.name());
                        String value = entry.getValue();
                        if (StringUtils.isNotEmpty(value)) {
                            // 对非中文字符进行 URL 编码
                            if (!isChinese(value)) {
                                value = URLEncoder.encode(value, StandardCharsets.UTF_8.name());
                            }

                            value = key + "=" + value;
                        } else {
                            value = key + "=";
                        }

                        return value;
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("Error encoding parameter: {}", JSON.toJSONString(entry));
                        return "";
                    }
                })
                .collect(Collectors.joining("&"));
    }

    private static boolean isChinese(String str) {
        return str.codePoints().anyMatch(
                codePoint -> Character.UnicodeBlock.of(codePoint) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
        );
    }
}
