package com.datatech.slgzt.model.user;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
* 用户;主要存储移动云套餐的外部用户、云管理平台各级管理员、云平台管理员 --实体类
* <AUTHOR>
* @date 2024/11/18 16:42
**/
@Data
public class OacUser  {

    private Long id;

    private Long createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    private Long updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    private Integer status;

    /**
     * 登录账号(oa用户ID 具有唯一标识)
     */
    private String loginName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别: male: 男 female: 女
     */
    private String sex;

    /**
     * 账号密码
     */
    private String pwd;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 邮箱
     */
    private String userEmail;

    /**
     * 归属组织编号
     */
    private Long orgId;

    /**
     * 归属租户编号
     */
    private Long tenantId;

    /**
     *部门dn, OU=信息技术部,O=zmcc
     */
    private String departmentDn;

    /**
     * oa-最后变更时间
     */
    private LocalDateTime oaLastTime;

    /**
     *职位名称
     */
    private String jobName;

    /**
     *排序
     */
    private Integer sort;

    /**
     *激活状态(1: 激活 0: 未激活 2:已冻结)
     */
    private Integer activeStatus;

    /**
     * 所属部门组织全称
     */
    private String userOrgName;

    /**
     * 用户角色列表
     */
    private List<OacRoleInfo> oacRoles;

    /**
     * 租户名字
     */
    private String tenantName;

    /**
     *
     * 创建人名字
     */
    private String createUserName;

    /**
     * 英文逗号拼接的拥有的角色
     */
    private String oacRolesName;

    /**
     * 激活状态-中文
     */
    private String activeStatusName;

    private String orgName;

    private List<UserCenterTenant> tenants;

}