package com.datatech.slgzt.model.vo.recovery.export;

import lombok.Data;

import java.util.List;

@Data
public class ResourceExport {

    private List<EcsExportVO> ecsExportVOS;

    private List<EcsExportVO> gcsExportVOS;

    private List<EvsExportVO> evsExportVOS;

    private List<EipExportVO> eipExportVOS;

    private List<NatExportVO> natExportVOS;

    private List<SlbExportVO> slbExportVOS;

    private List<ObsExportVO> obsExportVOS;

    private List<BackupExportVO> backupExportVOS;

    private List<NetworkExportVO> networkExportVOS;

    private List<VpcExportVO> vpcExportVOS;

    private List<VpnExportVO> vpnExportVOS;

    private List<NasExportVO> nasExportVOS;

    private List<PmExportVO> pmExportVOS;

    private List<KafkaExportVO> kafkaExportVOS;

    private List<FlinkExportVO> flinkExportVOS;

    private List<EsExportVO> esExportVOS;

}

