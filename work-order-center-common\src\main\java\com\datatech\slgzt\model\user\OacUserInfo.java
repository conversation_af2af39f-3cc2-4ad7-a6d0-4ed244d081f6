package com.datatech.slgzt.model.user;

import lombok.Data;

import java.util.List;

/**
 * 用户详细信息
 * <AUTHOR>
 * @Date: 2024/11/18 15:49
 * @Description:
 */
@Data
public class OacUserInfo {

    private Long id;

    /**
     * 登录账号
     */
    private String loginName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 性别: male: 男 female: 女
     */
    private String sex;

    /**
     * 账号密码
     */
    private String pwd;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 邮箱
     */
    private String userEmail;

    /**
     * 归属组织编号
     */
    private Long orgId;

    /**
     * 归属租户编号
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 归属部门
     */
    private String orgName;

    /**
     * 归属部门(展示两级部门)
     */
    private String sysDeptName;

    /**
     * 归属部门(展示两级部门)
     */
    private String departmentDn;

    /**
     *职位名称
     */
    private String jobName;

    /**
     * 用户角色列表
     */
    private List<OacRoleInfo> oacRoles;

    /**
     * 令牌
     */
    private String token;

    /**
     * 版本号
     */
    private String versionId;

    /**
     * cmp租户编号
     */
    private String cmpTenantId;

    /**
     * 计费号
     */
    private String billId;

    /**
     * 集团编号
     */
    private String customNo;

    /**
     * 菜单类型
     */
    private String menuType;

    /**
     * 是否运营组
     */
    private boolean operation = false;
}
