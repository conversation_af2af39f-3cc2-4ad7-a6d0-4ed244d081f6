package com.datatech.slgzt.model.home;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 首页资源基准通用模型做计算算法使用
 *
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/16
 */

@Data
@Accessors(chain = true)
public class HomeResourceBaseModel implements Serializable {

    /**
     * 商品类型
     */
    private String goodType;

    /**
     * 计算单元
     *
     * @mack 0
     */
    private Integer amount = 0;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private Integer status;


}

