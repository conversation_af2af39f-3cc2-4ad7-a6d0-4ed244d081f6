package com.datatech.slgzt.consumer;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class WorkOrderTopicConsumer {
    private final static String WORK_ORDER_TOPIC = "prod_oac_resource_detail_topic_lj";

    @Resource
    private StandardResourceDetailConsumer standardResourceDetailConsumer;
    @Resource
    private NonStanderResourceDetailConsumer nonStanderResourceDetailConsumer;
    @Resource
    private ChangeResourceDetailConsumer changeResourceDetailConsumer;
    @Resource
    private CorporateResourceDetailConsumer corporateResourceDetailConsumer;
    @Resource
    private DagResourceDetailConsumer dagResourceDetailConsumer;

    @KafkaListener(groupId = "prod-work-order-resource-detail-group-standard", topics = {WORK_ORDER_TOPIC})
    public void consumeResourceMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
        log.info("监听任务消息: {}", consumerRecordList.size());
        //转换消息放入集合中
        List<ResourceDetailDTO> subscribeList = new ArrayList<>();
        List<ResourceDetailDTO> nonStandardList = new ArrayList<>();
        List<ResourceDetailDTO> changeList = new ArrayList<>();
        List<ResourceDetailDTO> externalList = new ArrayList<>();
        List<ResourceDetailDTO> corporateList = new ArrayList<>();
        List<ResourceDetailDTO> dagList = new ArrayList<>();
        for (ConsumerRecord<String, String> record : consumerRecordList) {
            ResourceDetailDTO resourceDetail = JSONObject.parseObject(record.value(), ResourceDetailDTO.class);
            log.info("消息体解析：{}", resourceDetail);
            if (OrderTypeEnum.SUBSCRIBE.getCode().equals(resourceDetail.getSourceExtType())) {
                subscribeList.add(resourceDetail);
            } else if (OrderTypeEnum.NON_STANDARD.getCode().equals(resourceDetail.getSourceExtType())) {
                nonStandardList.add(resourceDetail);
            } else if (OrderTypeEnum.CHANGE.getCode().equals(resourceDetail.getSourceExtType())) {
                changeList.add(resourceDetail);
            } else if (OrderTypeEnum.EXTERNAL.getCode().equals(resourceDetail.getSourceExtType())) {
                externalList.add(resourceDetail);
            } else if (OrderTypeEnum.CORPORATE.getCode().equals(resourceDetail.getSourceExtType())) {
                corporateList.add(resourceDetail);
            } else if (OrderTypeEnum.DAG.getCode().equals(resourceDetail.getSourceExtType())) {
                dagList.add(resourceDetail);
            }
        }
        if (CollectionUtil.isNotEmpty(subscribeList) || CollectionUtil.isNotEmpty(externalList)) {
            standardResourceDetailConsumer.consumeResourceMessage(subscribeList, externalList);
        }
        if (!CollectionUtil.isEmpty(changeList)) {
            changeResourceDetailConsumer.consumeResourceMessage(changeList);
        }
        if (CollectionUtil.isNotEmpty(nonStandardList)) {
            nonStanderResourceDetailConsumer.consumeResourceMessage(nonStandardList);
        }
        if (CollectionUtil.isNotEmpty(corporateList)) {
            corporateResourceDetailConsumer.consumeResourceMessage(corporateList);
        }
        if (CollectionUtil.isNotEmpty(dagList)) {
            dagResourceDetailConsumer.consumeResourceMessage(dagList);
        }
    }
}
