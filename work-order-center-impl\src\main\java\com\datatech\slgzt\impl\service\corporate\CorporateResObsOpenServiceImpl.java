package com.datatech.slgzt.impl.service.corporate;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datatech.slgzt.dao.model.ObsDAO;
import com.datatech.slgzt.dao.model.ext.CreateBucketDO;
import com.datatech.slgzt.dao.model.ext.ObsDO;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.exception.BusinessException;
import com.datatech.slgzt.impl.service.standard.StandardEcsCombinationResOpenStrategyServiceProvider;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.manager.CorporateOrderProductManager;
import com.datatech.slgzt.manager.DagOrderManager;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.ObsModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.model.query.ObsQuery;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.corporate.CorporateResOpenService;
import com.datatech.slgzt.service.dag.DagResOpenService;
import com.datatech.slgzt.utils.HttpClientUtil;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-03-14 10:52
 **/
@Service
@Slf4j
public class CorporateResObsOpenServiceImpl implements CorporateResOpenService {

    @Resource
    private CorporateOrderProductManager productManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private CorporateOrderManager corporateOrderManager;
    @Resource
    private ObsDAO obsDAO;
    //    @Value("${http.layoutCenterUrl}")
    private String layoutCenter = "http://127.0.0.1:9095/";
    @Value("${http.resourceCenterUrl}")
    private String resourceCenter;
    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";
    private final String resourceBucketCreate = "v1/cloud/resourcecenter/obs/bucket/create";

    @Override
    public Object openResource(CorporateOrderProductDTO productDTO) {
        CorporateOrderDTO orderDTO = corporateOrderManager.getById(productDTO.getOrderId());
        ObsModel obsModel = JSON.parseObject(productDTO.getPropertySnapshot(), ObsModel.class);
        Long tenantId = platformService.getOrCreateTenantId(obsModel.getBillId(), obsModel.getRegionCode());
        //------------------基础参数设置----------------------------------------------------------
        ResOpenReqModel openReqModel = new ResOpenReqModel();
        //--------------------基础部分设置----------------------------------------
        //设置计费号
        openReqModel.setAccount(obsModel.getBillId());
        //设置业务code;
        openReqModel.setSourceExtType(OrderTypeEnum.CORPORATE.getCode());
        //设置业务code
        openReqModel.setBusinessCode("OBS_CREATE");
        //设置业务系统code
        openReqModel.setBusinessSystemCode(String.valueOf(obsModel.getBusinessSystemId()));
        //设置客户id
        openReqModel.setCustomId(obsModel.getCustomNo());
        //设置区域编码
        openReqModel.setRegionCode(obsModel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        openReqModel.setSubOrderId(productDTO.getSubOrderId());
        //设置租户id
        openReqModel.setTenantId(tenantId);
        //设置userId
        openReqModel.setUserId(orderDTO.getCreateBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        openReqModel.setTaskSource(6);
        //开通资源
        List<ResOpenReqModel.ProductOrder> reqProductList = Lists.newArrayList();
        //------------设置obs的参数-----------------------------------------------------
        List<ResOpenReqModel.ProductOrder> obsProduct = StandardEcsCombinationResOpenStrategyServiceProvider.INSTANCE.get(ProductTypeEnum.OBS).assembleParam(new ResOpenOpm()
                .setGId(productDTO.getGid())
                .setTenantId(tenantId)
                .setCustomId(obsModel.getCustomNo())
                .setAccount(obsModel.getBillId())
                .setBusinessSystemCode(String.valueOf(obsModel.getBusinessSystemId()))
                .setSubOrderId(productDTO.getSubOrderId().toString())
                .setObsModelList(Lists.newArrayList(obsModel)));
        reqProductList.addAll(obsProduct);
        openReqModel.setProductOrders(reqProductList);
        //obs服务不存在，则开通服务+默认桶；否则在已创建的服务下开通桶
        ObsQuery obsQuery = new ObsQuery();
        obsQuery.setGroupId(obsModel.getCustomNo()).setBillId(obsModel.getBillId()).setRegionCode(obsModel.getRegionCode());
        List<ObsDO> obsDOS = checkServiceExit(obsQuery);
        if (CollectionUtil.isNotEmpty(obsDOS)) {
            openReqModel.setBusinessCode("OBS_MODIFY_QUOTA");
            ObsDO obsDO = obsDOS.get(0);
            //更新obs配额，修改任务类型和配额
            obsProduct.forEach(op -> {
                op.setProductOrderType("OBS_MODIFY_QUOTA");
                op.getAttrs().setGId(obsDO.getInstanceId());
                op.getAttrs().setQuota(obsDO.getQuota() + obsModel.getStorageDiskSize());
            });
            //obs更新配额
            //把对应的产品都改成开通中状态
            productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
            productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
            Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(openReqModel))
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "obs配额变更失败，callLayoutOrder--编排中心初始化返回结果失败, " + dataMapper.getString("message"));
            log.info("obs配额变更，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));
            //配额变更后休眠30秒再进行桶创建
            try {
                Thread.sleep(60000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            //创建桶
            CreateBucketDO createBucketDO = new CreateBucketDO();
            //桶参数赋值
            createBucketDO.setRegionCode(obsModel.getRegionCode()).setBillId(obsModel.getBillId()).setGroupId(obsModel.getCustomNo()).
                    setOptUuid(String.valueOf(productDTO.getSubOrderId())).setSourceOrderCode(orderDTO.getId()).setSystemSource("OAC").
                    setBucketName(obsModel.getObsName()).setBucketSize(obsModel.getStorageDiskSize()).setWriteProtect(0).setMultiVersion(0).
                    setStorageType("STANDARD").setStrategy("PRIVICE");
            Map<String, Object> response = HttpClientUtil.post(resourceCenter + resourceBucketCreate, JSONObject.toJSONString(createBucketDO), getHeaders());
            log.info("桶创建请求路径：{}，参数：{}，返回数据：{}", resourceCenter + resourceBucketCreate, JSONObject.toJSONString(createBucketDO), response);
            Integer code = (Integer) response.get("code");
            String json = (String) response.get("json");
            if (code != 200) {
                String message = "obs桶创建失败";
                if (StringUtils.isNotEmpty(json)) {
                    JSONObject jsonObject = JSONObject.parseObject(json);
                    message = jsonObject.get("message").toString();
                }
                productDTO.setOpenStatus(ResOpenEnum.OPEN_FAIL.getCode());
                productDTO.setMessage(message);
                productManager.update(productDTO);
                productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_FAIL.getCode());
                throw new BusinessException("调用资源中心桶开通失败：" + message);
            }
        } else {
            //把对应的产品都改成开通中状态
            productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
            productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
            //------------------调用底层开通接口-------------------------------------------------------
            log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}", JSON.toJSON(orderDTO.getId()), layoutCenter + layoutTaskInitUrl);
            Mapper dataMapper = OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                    .bodyType(OkHttps.JSON)
                    .setBodyPara(JSON.toJSONString(openReqModel))
                    .post()
                    .getBody()
                    .toMapper();
            String success = dataMapper.getString("success");
            Precondition.checkArgument("1".equals(success), "资源开通失败，callLayoutOrder--编排中心初始化返回结果失败, " + dataMapper.getString("message"));
            log.info("算力编排资源开通，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));

        }
        return null;
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.OBS;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }

    /**
     * 查询obs服务是否存在
     *
     * @return
     */
    private List<ObsDO> checkServiceExit(ObsQuery query) {
        Page<ObsDO> obsDOPage = obsDAO.queryObs(query);
        List<ObsDO> obsList = JSONArray.parseArray(JSON.toJSONString(obsDOPage.getRecords())).toJavaList(ObsDO.class);
        List<ObsDO> creating = obsList.stream().filter(obs -> "CREATING".equals(obs.getStatus())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(creating)) {
            log.debug("对象服务开通中，等待开通完成后重试");
            try {
                Thread.sleep(3000);
                obsDOPage = obsDAO.queryObs(query);
                obsList = JSONArray.parseArray(JSON.toJSONString(obsDOPage.getRecords())).toJavaList(ObsDO.class);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return obsList.stream().filter(obs -> !"DELETED".equals(obs.getStatus())).collect(Collectors.toList());
    }

    private Map<String, String> getHeaders() {
        Map<String, String> header = new HashMap<>();
        header.put("RemoteUser", "BusinessCenter");
        header.put("Content-Type", "application/json");
        return header;
    }
}
