package com.datatech.slgzt.component;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
public class FtpConfig {

    @Value("${ftp.service.ip}")
    private String ip;

    @Value("${ftp.service.port}")
    private Integer port;

    @Value("${ftp.service.user}")
    private String user;

    @Value("${ftp.service.pass}")
    private String pass;

    @Value("${ftp.service.basePath}")
    private String basePath;

    @Value("${ftp.service.templatePath}")
    private String templatePath;

    @Value("${ftp.service.localPath}")
    private String localPath;

}
