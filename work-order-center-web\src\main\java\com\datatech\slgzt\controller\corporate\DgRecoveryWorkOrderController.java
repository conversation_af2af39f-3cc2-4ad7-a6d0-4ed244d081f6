package com.datatech.slgzt.controller.corporate;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.DgRecoveryOrderWebConvert;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.DgRecoveryOrderDTO;
import com.datatech.slgzt.model.dto.DgRecoveryOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.opm.DgRecoveryOrderCreateOpm;
import com.datatech.slgzt.model.query.DgRecoveryOrderQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.req.corporate.recovery.DgRecoveryOrderCancelReq;
import com.datatech.slgzt.model.req.corporate.recovery.DgRecoveryOrderCreateReq;
import com.datatech.slgzt.model.req.corporate.recovery.DgRecoveryOrderDeleteAccountReq;
import com.datatech.slgzt.model.req.corporate.recovery.DgRecoveryOrderPageReq;
import com.datatech.slgzt.model.req.recovery.RecoveryWorkOrderDetailReq;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.corporate.DgRecoveryOrderVO;
import com.datatech.slgzt.model.vo.corporate.DgRecoveryWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.service.corporate.DgRecoveryOrderService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 回收工单控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:39:28
 */
@RestController
@RequestMapping("/dg/recovery/workOrder")
@Slf4j
public class DgRecoveryWorkOrderController {


    @Resource
    private BusinessService businessService;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private DgRecoveryOrderService dgRecoveryOrderService;

    @Resource
    private DgRecoveryOrderWebConvert convert;

    @Resource
    private DgRecoveryOrderManager orderManager;

    @Resource
    private DgRecoveryOrderProductManager productManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private NetworkOrderManager networkOrderManager;


    /**
     * 分页查询
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageResult<DgRecoveryOrderVO>> page(@RequestBody DgRecoveryOrderPageReq req) {
        DgRecoveryOrderQuery query = convert.pageReq2Query(req);
        Long userId = UserHelper.INSTANCE.getCurrentUserId();
        Precondition.checkArgument(userId, "当前用户未登录");
        PageResult<DgRecoveryOrderDTO> page = dgRecoveryOrderService.page(query, userId);
        PageResult<DgRecoveryOrderVO> box = PageWarppers.box(page, convert::dto2vo);
        return CommonResult.success(box);
    }

    /**
     * 创建回收工单
     */
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @OperationLog(description = "创建对公回收订单", operationType = "CREATE")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> create(@RequestBody DgRecoveryOrderCreateReq req) {
        //如果怕并发可以用租户维度去加锁，上面还缺少不是自己的设备 不能回收的校验 现在前端控制先
        //校验完毕可以传入对应的id去创建了
        DgRecoveryOrderCreateOpm opm = convert.convert(req);
//        //查询 计费号和客户号
//        CmpAppDTO cmpAppDTO = businessService.getById(req.getBusinessSystemId());
//        Precondition.checkArgument(cmpAppDTO, "找不到对应的业务系统");
//        Precondition.checkArgument(cmpAppDTO.getTenantId(), "找不到对应的业务系统");
//        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
//        Precondition.checkArgument(tenantDTO, "找不到对应的租户");
//        Precondition.checkArgument(tenantDTO.getCustomNo(), "找不到对应的客户号");
//        Precondition.checkArgument(tenantDTO.getBillId(), "找不到对应的计费号");
//        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
//        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
//        opm.setCreatedBy(currentUser.getId());
//        opm.setCreatedUserName(currentUser.getUserName());
//        opm.setCreateUserId(currentUser.getId());
//        opm.setTenantId(cmpAppDTO.getTenantId());
//        opm.setTenantName(tenantDTO.getName());
//        opm.setBusinessSystemName(cmpAppDTO.getSystemName());
//        opm.setBusinessSystemCode(cmpAppDTO.getSystemCode());
//        opm.setBillId(tenantDTO.getBillId());
//        opm.setCustomNo(tenantDTO.getCustomNo());
//        if(ObjNullUtils.isNull(opm.getId())){
//            dgRecoveryOrderService.fillCheckCreate(opm);
//        }
        dgRecoveryOrderService.fillCheckCreate(opm);
        String orderId = dgRecoveryOrderService.createRecoveryWorkOrder(opm);
        return CommonResult.success(orderId);

    }

    /**
     * 用户注销
     */
    @RequestMapping(value = "/deleteAccount", method = RequestMethod.POST)
    @OperationLog(description = "创建对公回收订单", operationType = "CREATE")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> deleteAccount(@RequestBody DgRecoveryOrderDeleteAccountReq req) {
        // billId不能为null
        Precondition.checkArgument(req.getBillId(), "计费号不能为空");

        List<ResourceDetailDTO> resourceDetailDTOS = resourceDetailManager.list(new ResourceDetailQuery().setBillId(req.getBillId()));

        DgRecoveryOrderCreateOpm opm = new DgRecoveryOrderCreateOpm();
        opm.setRecoveryDeviceIdList(Lists.newArrayList());
        opm.setEcsIdList(Lists.newArrayList());
        opm.setGcsIdList(Lists.newArrayList());
        opm.setRdsMysqlIdList(Lists.newArrayList());
        opm.setRedisIdList(Lists.newArrayList());
        opm.setEvsIdList(Lists.newArrayList());
        opm.setEipIdList(Lists.newArrayList());
        opm.setNatIdList(Lists.newArrayList());
        opm.setObsIdList(Lists.newArrayList());
        opm.setSlbIdList(Lists.newArrayList());
        opm.setVpcIdList(Lists.newArrayList());
        opm.setNetworkIdList(Lists.newArrayList());
        opm.setBackupIdList(Lists.newArrayList());
        opm.setVpnIdList(Lists.newArrayList());
        opm.setNasIdList(Lists.newArrayList());
        opm.setPmIdList(Lists.newArrayList());
        opm.setSyncRecoveryIdList(Lists.newArrayList());
        opm.setCanDraft(false);
        opm.setCreatedBy(0L);
        opm.setCreatedUserName("");
        opm.setCreateUserId(0L);

        for (ResourceDetailDTO resourceDetailDTO : resourceDetailDTOS) {
            switch (resourceDetailDTO.getType()) {
                case "ecs":
                    opm.getEcsIdList().add(resourceDetailDTO.getId());
                    break;
                case "gcs":
                    opm.getGcsIdList().add(resourceDetailDTO.getId());
                    break;
                case "rdsMysql":
                    opm.getRdsMysqlIdList().add(resourceDetailDTO.getId());
                    break;
                case "redis":
                    opm.getRedisIdList().add(resourceDetailDTO.getId());
                    break;
                case "evs":
                    opm.getEvsIdList().add(resourceDetailDTO.getId());
                    break;
                case "eip":
                    opm.getEipIdList().add(resourceDetailDTO.getId());
                    break;
                case "nat":
                    opm.getNatIdList().add(resourceDetailDTO.getId());
                    break;
                case "obs":
                    opm.getObsIdList().add(resourceDetailDTO.getId());
                    break;
                case "slb":
                    opm.getSlbIdList().add(resourceDetailDTO.getId());
                    break;
//                case "vpc":
//                    opm.getVpcIdList().add(resourceDetailDTO.getId());
//                    break;
//                case "network":
//                    opm.getNetworkIdList().add(resourceDetailDTO.getId());
//                    break;
                case "backup":
                    opm.getBackupIdList().add(resourceDetailDTO.getId());
                    break;
                case "vpn":
                    opm.getVpnIdList().add(resourceDetailDTO.getId());
                    break;
                case "nas":
                    opm.getNasIdList().add(resourceDetailDTO.getId());
                    break;
                case "pm":
                    opm.getPmIdList().add(resourceDetailDTO.getId());
                    break;
                default:
                    break;
            }
        }
        dgRecoveryOrderService.fillCheckCreate(opm);
        String orderId = dgRecoveryOrderService.createRecoveryWorkOrder(opm);
        return CommonResult.success(orderId);

    }

    /**
     * 详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public CommonResult<DgRecoveryWorkOrderDetailVO> detail(@RequestBody RecoveryWorkOrderDetailReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        //查询用户是否存在草稿缓存
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");

        DgRecoveryOrderDTO dto = orderManager.getById(req.getWorkOrderId());
        Precondition.checkArgument(dto, "工单异常不存在");
        DgRecoveryWorkOrderDetailVO vo = convert.convertDetail(dto);
        List<DgRecoveryOrderProductDTO> productDTOS = productManager.listByWorkOrderId(req.getWorkOrderId());
        //productDTOS 直接过滤掉子产品
        productDTOS = productDTOS.stream()
                .filter(productDTO -> productDTO.getParentProductId() == 0)
                .collect(Collectors.toList());
        ArrayListMultimap<String, DgRecoveryOrderProductDTO> type2product =
                StreamUtils.toArrayListMultimap(productDTOS, DgRecoveryOrderProductDTO::getProductType);
        //获取网络资源和vpc
        //获取网络id列表
        List<String> vpcIdList = StreamUtils.mapArrayFilterNull(type2product.get(ProductTypeEnum.VPC.getCode()),
                DgRecoveryOrderProductDTO::getResourceDetailId);
        List<String> networkIdList = StreamUtils.mapArrayFilterNull(type2product.get(ProductTypeEnum.NETWORK.getCode()),
                DgRecoveryOrderProductDTO::getResourceDetailId);
        //获取资源表里的要调用的就是排除掉网络和vpc的
        List<Long> resDatilsIds = productDTOS.stream()
                .filter(productDTO -> !productDTO.getProductType().equals(ProductTypeEnum.VPC.getCode()))
                .filter(productDTO -> !productDTO.getProductType().equals(ProductTypeEnum.NETWORK.getCode()))
                .filter(productDTO -> productDTO.getParentProductId() == 0)
                .filter(productDTO -> ObjNullUtils.isNotNull(productDTO.getResourceDetailId()))
                .map(DgRecoveryOrderProductDTO::getResourceDetailId)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        //获取资源
        List<ResourceDetailDTO> resourceDetailDTOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resDatilsIds)) {
            resourceDetailDTOS = resourceDetailManager.list(new ResourceDetailQuery().setIds(resDatilsIds));
        }
        //获取vpc
        List<VpcOrderResult> vpcOrderResultList = new ArrayList<>();
        if (ObjNullUtils.isNotNull(vpcIdList)) {
            vpcOrderResultList = vpcOrderManager.listByIdList(vpcIdList);
        }
        //获取网络
        List<NetworkOrderResult> networkOrderResultList = new ArrayList<>();
        if (ObjNullUtils.isNotNull(networkIdList)) {
            networkOrderResultList = networkOrderManager.selectNetworkRecoveryList(networkIdList);
        }
        convert.fillProductDetail(vo, resourceDetailDTOS, vpcOrderResultList, networkOrderResultList, type2product);

        return CommonResult.success(vo);
    }

    /**
     * 退订order中的某个product
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public CommonResult<String> cancel(@RequestBody DgRecoveryOrderCancelReq req) {
        Precondition.checkArgument(req.getProductIds(), "产品id不能为空");
        dgRecoveryOrderService.cancel(req.getProductIds());
        return CommonResult.success("退订成功");
    }

    /**
     * 回收
     */
    @RequestMapping(value = "/test", method = RequestMethod.POST)
    public CommonResult<String> recovery(@RequestBody RecoveryWorkOrderDetailReq req) {
        Precondition.checkArgument(req.getWorkOrderId(), "工单ID不能为空");
        dgRecoveryOrderService.recovery(req.getWorkOrderId());
        return CommonResult.success(req.getWorkOrderId());
    }

}