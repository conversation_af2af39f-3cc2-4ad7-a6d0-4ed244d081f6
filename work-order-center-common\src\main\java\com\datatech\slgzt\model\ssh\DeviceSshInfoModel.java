package com.datatech.slgzt.model.ssh;

import lombok.Data;

/**
 * 设备SSH连接信息
 */
@Data
public class DeviceSshInfoModel {


    
    /**
     * 主机地址
     */
    private String host;
    
    /**
     * 端口
     */
    private Integer port;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码（加密存储）
     */
    private String password;
    
    /**
     * 连接类型：PASSWORD-密码, KEY-密钥
     */
    private String connectionType;
    
    /**
     * 私钥（加密存储，可选）
     */
    private String privateKey;

}