package com.datatech.slgzt.model.nostander;

import com.datatech.slgzt.model.BaseProductModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 20:53:25
 */
@Data
public class NatGatwayModel extends BaseProductModel {


    private String flavorCode;

    private String flavorId;

    /**
     * 规格类型:使用CATEGORY_NAME字段
     */
    private String flavorType;

    /**
     * 规格名称
     */
    private String flavorName;

    private String systemSource;

    private String natName;

    private String time;

    //开通数量
    private Integer openNum;

    private String productType;

    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 是否绑定公网IP
     */
    private Boolean bindPublicIp;

    /**
     * 如果bindPublicIp为true，则需要填写绑定公网IP的列表
     * 绑定公网IP的列表
     */
    private List<EipModel> EipModelList;


    private PlaneNetworkModel planeNetworkModel;

    /**
     * 云类型
     */
    private String catalogueDomainCode;


    /**
     * 云平台id
     */
    private String domainCode;


}
