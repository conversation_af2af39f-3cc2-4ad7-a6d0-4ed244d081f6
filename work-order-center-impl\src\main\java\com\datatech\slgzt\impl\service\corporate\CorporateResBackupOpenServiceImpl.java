package com.datatech.slgzt.impl.service.corporate;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.standard.StandardEcsCombinationResOpenStrategyServiceProvider;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.manager.CorporateOrderProductManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.BackupModel;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.corporate.CorporateResOpenService;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 云备份策略开通
 * @author: LK
 * @create: 2025-06-04 14:57
 **/
@Slf4j
@Service
public class CorporateResBackupOpenServiceImpl implements CorporateResOpenService {

    @Resource
    private CorporateOrderProductManager productManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private CorporateOrderManager corporateOrderManager;

    //    @Value("${http.layoutCenterUrl}")
    private String layoutCenter = "http://127.0.0.1:9095/";

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public Object openResource(CorporateOrderProductDTO productDTO) {
        //获取工单
        CorporateOrderDTO orderDTO = corporateOrderManager.getById(productDTO.getOrderId());
        BackupModel backupModel = JSON.parseObject(productDTO.getPropertySnapshot(), BackupModel.class);
        Long tenantId = platformService.getOrCreateTenantId(backupModel.getBillId(), backupModel.getRegionCode());
        //------------------基础参数设置----------------------------------------------------------
        String backupType = backupModel.getBackupType();
        ResOpenReqModel resOpenReqModel = new ResOpenReqModel();
        //--------------------基础部分设置----------------------------------------
        //设置计费号
        resOpenReqModel.setAccount(backupModel.getBillId());
        //设置业务code;
        resOpenReqModel.setSourceExtType(OrderTypeEnum.CORPORATE.getCode());
        //设置业务code
        String businessCode = "ECS".equals(backupType) ? "BACKUP_ECS_CREATE" : "EVS".equals(backupType) ? "BACKUP_EVS_CREATE" : "";
        resOpenReqModel.setBusinessCode(businessCode);
        //设置业务系统code
        resOpenReqModel.setBusinessSystemCode(backupModel.getBusinessSystemId().toString());
        //设置客户id
        resOpenReqModel.setCustomId(backupModel.getCustomNo());
        //设置区域编码
        resOpenReqModel.setRegionCode(backupModel.getRegionCode());
        //设置的是主产品的gid 这里适配任务中心回调
        resOpenReqModel.setSubOrderId(productDTO.getSubOrderId());
        resOpenReqModel.setGid(productDTO.getGid());
        //设置租户id 可能是需要传入底层租户id 应该要查询下 目前不知道查询的方式
        resOpenReqModel.setTenantId(tenantId);
        //设置userId
        resOpenReqModel.setUserId(orderDTO.getCreateBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resOpenReqModel.setTaskSource(6);
        //开通资源
        List<ResOpenReqModel.ProductOrder> reqProductList= Lists.newArrayList();
        //------------设置nat的参数-----------------------------------------------------
        List<ResOpenReqModel.ProductOrder> natProduct = StandardEcsCombinationResOpenStrategyServiceProvider.INSTANCE.get(ProductTypeEnum.BACKUP).assembleParam(new ResOpenOpm()
                .setTenantId(tenantId)
                .setCustomId(backupModel.getCustomNo())
                .setAccount(backupModel.getBillId())
                .setBusinessSystemCode(backupModel.getBusinessSystemId().toString())
                .setSubOrderId(productDTO.getSubOrderId().toString())
                .setGId(productDTO.getGid())
                .setBackupModelList(Lists.newArrayList(backupModel)));
        reqProductList.addAll(natProduct);

        resOpenReqModel.setProductOrders(reqProductList);
        //------------------产品参数设置结束-------------------------------------------------------
        //把对应的产品都改成开通中状态
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        log.info("资源开通，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={},request param={}", JSON.toJSON(orderDTO.getId()), layoutCenter + layoutTaskInitUrl, JSON.toJSONString(resOpenReqModel));
        Mapper dataMapper= OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(resOpenReqModel))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "资源开通失败，callLayoutOrder--编排中心初始化返回结果失败, " + dataMapper.getString("message"));
        log.info("资源开通，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}", JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));
        return null;
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.BACKUP;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {

    }
}
