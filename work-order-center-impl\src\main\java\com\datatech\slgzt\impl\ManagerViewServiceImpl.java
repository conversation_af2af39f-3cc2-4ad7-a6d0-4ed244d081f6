package com.datatech.slgzt.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.datatech.slgzt.dao.mapper.VMResourcePerformanceMapper;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.manager.CorporateOrderManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.VMResourcePerformanceManager;
import com.datatech.slgzt.model.CustomCountDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.query.CorporateOrderQuery;
import com.datatech.slgzt.model.query.CustomQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.service.ManagerViewService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-07-01 16:03
 **/
@Service
public class ManagerViewServiceImpl implements ManagerViewService {

    @Resource
    private VmMapper vmMapper;

    @Resource
    private CorporateOrderManager corporateOrderManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VMResourcePerformanceManager vmResourcePerformanceManager;

    /**
     * 客户经理统计
     * @param userId 用户id
     * @return
     */
    @Override
    public CustomCountDTO customCount(Long userId) {
        CustomCountDTO dto = new CustomCountDTO();
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        Precondition.checkArgument(!CollectionUtils.isEmpty(customTenants), "当前用户没有客户经理视图相关数据");

        // 计算客户总数
        dto.setTotalCustom(customTenants.stream()
                .map(CustomTenantDTO::getCustomId)
                .distinct()
                .count());

        List<Long> tenantIds = customTenants.stream()
                .map(CustomTenantDTO::getTenantId)
                .collect(Collectors.toList());

        // 处理订单数据
        List<CorporateOrderDTO> orders = corporateOrderManager.list(
                new CorporateOrderQuery().setTenantIds(tenantIds));
        dto.setTotalOrder(CollectionUtils.isEmpty(orders) ? 0 : orders.size());
        dto.setMonthTotalOrder(CollectionUtils.isEmpty(orders) ? 0L :
                orders.stream()
                        .filter(o -> o.getCreateTime() != null)
                        .filter(o -> YearMonth.from(o.getCreateTime()).equals(YearMonth.now()))
                        .count());

        // 处理产品数据
        List<ResourceDetailDTO> products = resourceDetailManager.list(
                new ResourceDetailQuery().setTenantList(tenantIds).setSourceType("DG"));
        dto.setTotalProduct(CollectionUtils.isEmpty(products) ? 0 : products.size());
        dto.setMonthTotalProduct(CollectionUtils.isEmpty(products) ? 0L :
                products.stream()
                        .filter(p -> p.getCreateTime() != null)
                        .filter(p -> YearMonth.from(p.getCreateTime()).equals(YearMonth.now()))
                        .count());

        return dto;
    }

    /**
     * 客户集团列表
     * @param userId
     * @return
     */
    @Override
    public List<CustomTenantDTO> customDetail(Long userId) {
        // 1. 获取基础数据并校验
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        Precondition.checkArgument(!CollectionUtils.isEmpty(customTenants), "当前用户没有客户经理视图相关数据");

        // 2. 按customId分组处理
        return customTenants.stream()
                .collect(Collectors.groupingBy(CustomTenantDTO::getCustomId))
                .values()
                .stream()
                .map(customTenantDTOS -> {
                    CustomTenantDTO representative = customTenantDTOS.get(0);
                    List<Long> tenantIds = customTenantDTOS.stream()
                            .map(CustomTenantDTO::getTenantId)
                            .collect(Collectors.toList());

                    // 处理订单数据
                    List<CorporateOrderDTO> orders = corporateOrderManager.list(
                            new CorporateOrderQuery().setTenantIds(tenantIds));
                    representative.setTotalOrder(CollectionUtils.isEmpty(orders) ? 0 : orders.size());
                    representative.setMonthTotalOrder(
                            CollectionUtils.isEmpty(orders) ? 0L :
                                    orders.stream()
                                            .filter(order -> order.getCreateTime() != null)
                                            .filter(order -> YearMonth.from(order.getCreateTime()).equals(YearMonth.now()))
                                            .count());

                    // 处理产品数据
                    List<ResourceDetailDTO> products = resourceDetailManager.list(
                            new ResourceDetailQuery().setTenantList(tenantIds).setSourceType("DG"));
                    representative.setTotalProduct(CollectionUtils.isEmpty(products) ? 0 : products.size());

                    return representative;
                })
                .sorted(Comparator.comparingInt(CustomTenantDTO::getTotalOrder).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public List<VMResourcePerformanceDTO> selectTop5vCPUGroupByCustom(Long userId) {
        // 1. 获取基础数据并校验
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        Precondition.checkArgument(!CollectionUtils.isEmpty(customTenants), "当前用户没有客户经理视图相关数据");
        Map<String, List<CustomTenantDTO>> map = customTenants.stream().collect(Collectors.groupingBy(CustomTenantDTO::getCustomId));
        // 2. 获取性能数据
        return vmResourcePerformanceManager.selectTop5vCPUGroupByCustom(ListUtil.toList(map.keySet()));
    }

    @Override
    public List<CustomTenantDTO> totalProductTop5(Long userId) {
        // 1. 获取基础数据并校验
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        Precondition.checkArgument(!CollectionUtils.isEmpty(customTenants), "当前用户没有客户经理视图相关数据");

        // 2. 按customId分组处理
        return customTenants.stream()
                .collect(Collectors.groupingBy(CustomTenantDTO::getCustomId))
                .values()
                .stream()
                .map(customTenantDTOS -> {
                    CustomTenantDTO representative = customTenantDTOS.get(0);
                    List<Long> tenantIds = customTenantDTOS.stream()
                            .map(CustomTenantDTO::getTenantId)
                            .collect(Collectors.toList());

                    // 处理订单数据
                    List<CorporateOrderDTO> orders = corporateOrderManager.list(
                            new CorporateOrderQuery().setTenantIds(tenantIds));
                    representative.setTotalOrder(CollectionUtils.isEmpty(orders) ? 0 : orders.size());
                    representative.setMonthTotalOrder(
                            CollectionUtils.isEmpty(orders) ? 0L :
                                    orders.stream()
                                            .filter(order -> order.getCreateTime() != null)
                                            .filter(order -> YearMonth.from(order.getCreateTime()).equals(YearMonth.now()))
                                            .count());

                    // 处理产品数据
                    List<ResourceDetailDTO> products = resourceDetailManager.list(
                            new ResourceDetailQuery().setTenantList(tenantIds));
                    representative.setTotalProduct(CollectionUtils.isEmpty(products) ? 0 : products.size());

                    return representative;
                })
                .sorted(Comparator.comparingInt(CustomTenantDTO::getTotalProduct).reversed())
                .limit(5)
                .collect(Collectors.toList());
    }

    @Override
    public List<VMResourcePerformanceDTO> selectTop5MemGroupByCustom(Long userId) {
        // 1. 获取基础数据并校验
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        Precondition.checkArgument(!CollectionUtils.isEmpty(customTenants), "当前用户没有客户经理视图相关数据");
        Map<String, List<CustomTenantDTO>> map = customTenants.stream().collect(Collectors.groupingBy(CustomTenantDTO::getCustomId));
        // 2. 获取性能数据
        return vmResourcePerformanceManager.selectTop5MemGroupByCustom(ListUtil.toList(map.keySet()));
    }

    @Override
    public List<VMResourcePerformanceDTO> top5Resource(Long userId, String orderType) {
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(userId);
        Precondition.checkArgument(!CollectionUtils.isEmpty(customTenants), "当前用户没有客户经理视图相关数据");
        Map<String, List<CustomTenantDTO>> map = customTenants.stream().collect(Collectors.groupingBy(CustomTenantDTO::getCustomId));
        if ("vCPU".equals(orderType)) {
            return vmResourcePerformanceManager.selectTop5vCPUGroupByCustom(ListUtil.toList(map.keySet()));
        } else if ("MEM".equals(orderType)) {
            return vmResourcePerformanceManager.selectTop5MemGroupByCustom(ListUtil.toList(map.keySet()));
        } else {
            return null;
        }
    }

    @Override
    public List<VMResourcePerformanceDTO> top5ResourceOfCustom(String customId, String orderType) {
        if ("vCPU".equals(orderType)) {
            return vmResourcePerformanceManager.selectTop5vCPUGroupByCustom(ListUtil.toList(customId));
        } else if ("MEM".equals(orderType)) {
            return vmResourcePerformanceManager.selectTop5MemGroupByCustom(ListUtil.toList(customId));
        } else if ("IO".equals(orderType)) {
            return vmResourcePerformanceManager.selectTop5IORateGroupByCustom(ListUtil.toList(customId));
        } else {
            return null;
        }
    }

    @Override
    public CustomDTO getCustomDetail(String customId) {
        // 1. 获取客户基础信息及关联租户
        CustomDTO customDTO = vmMapper.getCustomById(customId);
        Precondition.checkArgument(customDTO, "客户不存在");

        // 2. 处理租户ID（从逗号分隔字符串转为List）
        List<Long> tenantIds = customDTO.getTenantId() == null ?
                Collections.emptyList() :
                Arrays.stream(customDTO.getTenantId().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toList());

        // 3. 查询并处理订单数据
        List<CorporateOrderDTO> orders = CollectionUtils.isEmpty(tenantIds) ?
                Collections.emptyList() :
                corporateOrderManager.list(new CorporateOrderQuery().setTenantIds(tenantIds));

        customDTO.setTotalOrder(orders.size());
        customDTO.setMonthTotalOrder(
                orders.stream()
                        .filter(order -> order.getCreateTime() != null)
                        .filter(order -> YearMonth.from(order.getCreateTime()).equals(YearMonth.now()))
                        .count()
        );

        // 4. 查询并处理产品数据
        List<ResourceDetailDTO> products = CollectionUtils.isEmpty(tenantIds) ?
                Collections.emptyList() :
                resourceDetailManager.list(new ResourceDetailQuery().setTenantList(tenantIds));

        customDTO.setTotalProduct(products.size());
        customDTO.setMonthTotalProduct(
                products.stream()
                        .filter(product -> product.getCreateTime() != null)
                        .filter(product -> YearMonth.from(product.getCreateTime()).equals(YearMonth.now()))
                        .count()
        );

        return customDTO;
    }

    @Override
    public PageResult<CustomDTO> page(CustomQuery customQuery) {
        List<CustomTenantDTO> customTenants = vmMapper.getCustomTenants(customQuery.getCurrentUserId());
        Precondition.checkArgument(!CollectionUtils.isEmpty(customTenants), "当前用户没有客户经理视图相关数据");
        Map<String, List<CustomTenantDTO>> map = customTenants.stream().collect(Collectors.groupingBy(CustomTenantDTO::getCustomId));
        customQuery.setCustomIds(ListUtil.toList(map.keySet()));
        List<CustomDTO> customList = vmMapper.getCustomList(customQuery);
        return PageWarppers.box(new PageInfo<>(customList));
    }
}
