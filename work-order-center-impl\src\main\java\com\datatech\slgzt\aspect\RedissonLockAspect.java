package com.datatech.slgzt.aspect;

import com.datatech.slgzt.annotation.Lock;
import com.datatech.slgzt.service.LockService;
import com.datatech.slgzt.utils.SpElUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * 分布式锁切面
 */
@Aspect
@Component
@Order(0)// 设置优先级,确保比事务注解aop先执行,后结束
public class RedissonLockAspect {

    @Resource
    private LockService lockService;

    /**
     * 环绕通知
     */
    @Around("@annotation(com.datatech.slgzt.annotation.Lock)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取aop切入的方法
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();

        // 获取分布式锁注解对象
        Lock redissonLock = method.getAnnotation(Lock.class);

        // key前缀
        String prefix = StringUtils.isBlank(redissonLock.prefixKey()) ? SpElUtils.getMethodKey(method) : redissonLock.prefixKey();

        // el解析获取分布式锁的key
        String redissonkey="";
        String[] keys = redissonLock.key();
        for (String key : keys) {
            redissonkey += SpElUtils.parseSpEl(method, joinPoint.getArgs(), key);
        }
        // 执行
        return lockService.executeWithLock(prefix + ":" + redissonkey, redissonLock.waitTime(), redissonLock.unit(), joinPoint::proceed);
    }
}
