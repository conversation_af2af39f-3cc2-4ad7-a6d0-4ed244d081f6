package com.datatech.slgzt.model.nostander;

import lombok.Data;

import java.util.List;

/**
 * 可能是传统的VPC模型
 * 也可能是网络模型
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 15:29:31
 */
@Data
public class PlaneNetworkModel {

    private String type;
    /**
     * vpcId
     */
    private String id;

    private String name;

    /**
     * 网络平面
     */
    private String plane;

    private List<Subnet> subnets;


    /**
     * 排序（按照从小到大顺序塞参数到资源开通请求参数中）
     */
    private Integer sort;

    @Data
    public static class Subnet {
        /**
         * 子网ID，用于标识子网
         */
        private String subnetId;

        /**
         * 子网名称
         */
        private String subnetName;

        /**
         * 子网IP（CIDR格式），如***********/24
         */
        private String subnetIp;

        /**
         * 业务IPv4地址，用于业务网络通信
         * 主要用于前端展示，在资源详情页面显示
         */
        private String businessIpv4;

        /**
         * 业务IPv6地址，用于业务网络通信
         * 主要用于前端展示，在资源详情页面显示
         */
        private String businessIpv6;

        /**
         * 管理IPv4地址，用于管理网络通信
         * 主要用于前端展示，在资源详情页面显示
         */
        private String manageIpv4;

        /**
         * 管理IPv6地址，用于管理网络通信
         * 主要用于前端展示，在资源详情页面显示
         */
        private String manageIpv6;

        /**
         * 地址IPv4，用于特定场景
         * 主要用于前端展示，在资源详情页面显示
         */
        private String addressIpv4;

        /**
         * 地址IPv6，用于特定场景
         * 主要用于前端展示，在资源详情页面显示
         */
        private String addressIpv6;

        /**
         * ip地址
         * 前端展示+发送给任务中心
         */
        private String ipAddress;

        /**
         * ip地址，数量大于1的时候，使用
         * 后端拿到后，会拆分放入ipAddress中
         * @see com.datatech.slgzt.model.req.standard.StandardWorkOrderResOpenReq#openResIds
         */
        private List<String> ipAddresses;
    }







}
