//package com.datatech.slgzt.consumer;
//
//import cn.hutool.core.collection.CollectionUtil;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.datatech.slgzt.enums.ProductTypeEnum;
//import com.datatech.slgzt.manager.NonStanderWorkOrderManager;
//import com.datatech.slgzt.manager.ProductManager;
//import com.datatech.slgzt.manager.ResourceDetailManager;
//import com.datatech.slgzt.model.dto.NonStanderWorkOrderDTO;
//import com.datatech.slgzt.model.dto.ProductDTO;
//import com.datatech.slgzt.model.dto.ResourceDetailDTO;
//import com.datatech.slgzt.model.nostander.EscModel;
//import com.datatech.slgzt.model.nostander.EscVpcModel;
//import com.datatech.slgzt.model.query.ProductQuery;
//import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
//import com.datatech.slgzt.service.UserService;
//import com.datatech.slgzt.utils.DateUtils;
//import com.datatech.slgzt.utils.ObjNullUtils;
//import com.datatech.slgzt.utils.StreamUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.kafka.clients.consumer.ConsumerRecord;
//import org.springframework.kafka.annotation.KafkaListener;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @description TODO
// * @date 2025年 03月12日 15:20:59
// */
//@Slf4j
//@Service
//public class NostanderResourceDetailConsumer {
//
//
//    @Resource
//    private NonStanderWorkOrderManager nonStanderWorkOrderManager;
//
//    @Resource
//    private UserService userService;
//
//    @Resource
//    private ResourceDetailManager resourceDetailManager;
//
//    @Resource
//    private ProductManager productManager;
//
//    private final static String WORK_ORDER_TOPIC = "prod_oac_resource_detail_topic";
//
//    @KafkaListener(groupId = "prod-work-order-resource-detail-group-nostander", topics = {WORK_ORDER_TOPIC})
//    public void consumeResourceMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
//        log.info("监听任务消息: {}", consumerRecordList.size());
//        //转换消息放入集合中
//        List<ResourceDetailDTO> list = new ArrayList<>();
//        for(ConsumerRecord<String, String> record : consumerRecordList) {
//            ResourceDetailDTO resourceDetail = JSONObject.parseObject(record.value(), ResourceDetailDTO.class);
//            if ("NonstandWorkOrder".equals(resourceDetail.getSourceExtType())) {
//                list.add(resourceDetail);
//            }
//        }
//        if (CollectionUtil.isEmpty(list)) {
//            return;
//        }
//        //处理集合封装资源详情
//        List<Long> gids = list.stream().map(o -> Long.parseLong(o.getGoodsOrderId())).collect(Collectors.toList());
//        log.info("监听任务消息: 非标工单Id列表={}", JSON.toJSON(gids));
//        List<ProductDTO> productDTOS = productManager.list(new ProductQuery().setGids(gids));
//        if (CollectionUtil.isNotEmpty(productDTOS)) {
//            productDTOS.forEach(productDTO -> {
//                NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(productDTO.getWorkOrderId());
//                ResourceDetailDTO detail = new ResourceDetailDTO();
//                if (Objects.nonNull(orderDTO)) {
//                    //detail中已知任务中心会传值过来的字段,goodsOrderId,deviceName,eip,bandWidth,spec,deviceId,resourcePoolId,resourcePoolName,deviceStatus,resourceApplyTime
//                    detail.setType(productDTO.getProductType());
//                    detail.setOrderId(orderDTO.getId());
//                    detail.setOrderCode(orderDTO.getWorkOrderCode());
//                    detail.setTenantId(orderDTO.getTenantId());
//                    detail.setTenantName(orderDTO.getTenantName());
//                    detail.setBusinessSysId(orderDTO.getBusinessSysId());
//                    detail.setBusinessSysName(orderDTO.getBusinessSysName());
//                    detail.setApplyUserId(orderDTO.getCreateUserId());
//                    UserCenterUserDTO user = userService.getUserById(orderDTO.getCreateUserId());
//                    detail.setApplyUserName(Optional.ofNullable(user).map(UserCenterUserDTO::getUserName).orElse(null));
//                    detail.setCreateTime(new Date());
//                    detail.setStatus(1);
//                }
//                switch(ProductTypeEnum.getByCode(detail.getType())) {
//                    case ECS:
//                        coverEcs(detail,productDTO);
//                        break;
//                    case GCS:
//                        coverEcs(detail,productDTO);
//                        break;
//                    case EVS:
//                        break;
//                    case OBS:
//                        break;
//                    case SLB:
//                        break;
//                    case NAT:
//                        break;
//                    default:
//                }
//
//                if (detail.getEffectiveTime() == null) {
//                    //如果生效时间取不到，就使用创建时间
//                    detail.setEffectiveTime(detail.getCreateTime());
//                }
//                list.add(detail);
//
//            });
//        }
//
//        log.info("kafka资源开通回调，待更新detail表数据：{}", list);
//        list.forEach(o -> {
//            try {
//                resourceDetailManager.saveResourceDetail(o);
//            }catch(Exception e) {
//                //这里最好入库对象 做补偿
//                log.error("kafka资源开通回调，待更新detail表数据异常：{}", JSON.toJSONString(o));
//            }
//        });
//    }
//
//
//
//    public void coverEcs(ResourceDetailDTO detail, ProductDTO productDTO) {
//        detail.setEffectiveTime(detail.getCreateTime());
//        String propertySnapshot = productDTO.getPropertySnapshot();
//        String ext = productDTO.getExt();
//        //todo
//        if (ObjNullUtils.isNotNull(ext)) {
//            EscVpcModel escVpcModel = JSON.parseObject(ext, EscVpcModel.class);
//            detail.setVpcId(escVpcModel.getVpcId());
//            detail.setVpcName(escVpcModel.getVpcName());
//            EscVpcModel.SubnetModel any = StreamUtils.findAny(escVpcModel.getSubnets());
//            detail.setSubnetId(any.getSubnetId());
//            detail.setSubnetName(any.getSubnetName());
//        }
//        EscModel escModel = JSON.parseObject(propertySnapshot, EscModel.class);
//        detail.setCloudPlatform(escModel.getDomainCode());
//        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(),escModel.getApplyTime()));
//        detail.setApplyTime(escModel.getApplyTime());//已转换成one_month
//        JSONObject jsonObject = JSONObject.parseObject(propertySnapshot);
//        detail.setResourcePoolId(escModel.getRegionId().toString());
//        detail.setResourcePoolName(escModel.getRegionName());
//    }
//
//}
