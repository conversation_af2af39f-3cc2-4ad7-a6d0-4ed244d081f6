package com.datatech.slgzt.model.recovery;

import lombok.Data;

import java.util.List;

/**
 * 可能是传统的VPC模型
 * 也可能是网络模型
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 15:29:31
 */
@Data
public class RecoveryPlaneNetworkModel {

    private String type;
    /**
     * vpcId
     */
    private String id;

    private String name;

    /**
     * 网络平面
     */
    private String plane;

    private List<Subnet> subnets;


    /**
     * 排序（按照从小到大顺序塞参数到资源开通请求参数中）
     */
    private Integer sort;

    @Data
    public static class Subnet {
        private String subnetId;
        private String subnetName;
        private String subnetIp;

    }







}
