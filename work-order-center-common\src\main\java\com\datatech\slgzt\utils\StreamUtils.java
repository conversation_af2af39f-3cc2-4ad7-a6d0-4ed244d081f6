package com.datatech.slgzt.utils;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

public class StreamUtils {

    public static <T, R> List<R> mapArray(Collection<T> collection, Function<? super T, ? extends R> mapper) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().map(mapper).collect(Collectors.toList());

    }

    public static <T, R> List<R> mapArrayNotNull(Collection<T> collection, Function<? super T, ? extends R> mapper) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().map(mapper).filter(Objects::nonNull).collect(Collectors.toList());

    }

    public static <T, R> List<R> mapArrayFilterNull(Collection<T> collection, Function<? super T, ? extends R> mapper) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().map(mapper).filter(Objects::nonNull).collect(Collectors.toList());

    }

    //合并多个list
    public static <T> List<T> mergeList(List<T>... lists) {
        return Arrays.stream(lists).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * list去重
     *
     * @param collection
     * @param <T>
     * @return
     */
    public static <T> List<T> distinct(Collection<T> collection) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().distinct().collect(Collectors.toList());
    }

    public static <T> T findAny(Collection<T> collection) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().findAny().orElse(null);

    }


    public static <T> T findAny(Collection<T> collection, Supplier<T> emptySupplier) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().findAny().orElseGet(emptySupplier);
    }

    public static <T, R> Set<R> mapSet(Collection<T> collection, Function<? super T, ? extends R> mapper) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().map(mapper).collect(Collectors.toSet());

    }

    public static <T, K> Map<K, T> toMap(Collection<T> collection, Function<? super T, ? extends K> keyMapper) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(keyMapper, Function.identity(), (k1, k2) -> k1));

    }

    public static <T, K, V> Map<K, V> toMap(Collection<T> collection, Function<? super T,? extends K> keyMapper, Function<? super T,? extends V> valueMapper) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().collect(Collectors.toMap(keyMapper, valueMapper, (k1, k2) -> k1));
    }

    public static <T, K> ArrayListMultimap<K, T> toArrayListMultimap(Collection<T> collection, Function<? super T, ? extends K> keyMapper) {
        Collection<T> ts = Optional.ofNullable(collection).orElse(Lists.newArrayList());
        ArrayListMultimap<K, T> map = ArrayListMultimap.create();
        //这段用ArrayListMultimap 不是Map 用toMap实现不了
        ts.forEach(t -> map.put(keyMapper.apply(t), t));
        return map;
    }

    public static <T, K, V> ArrayListMultimap<K, V> toArrayListMultimap(Collection<T> collection, Function<? super T, ? extends K> keyMapper,
            Function<? super T, ? extends V> valueMapper) {
        Collection<T> ts = Optional.ofNullable(collection).orElse(Lists.newArrayList());
        ArrayListMultimap<K, V> map = ArrayListMultimap.create();
        //这段用ArrayListMultimap 不是Map 用toMap实现不了
        ts.forEach(t -> map.put(keyMapper.apply(t), valueMapper.apply(t)));
        return map;
    }


    public static <T, K> Map<K, List<T>> toMapList(Collection<T> collection, Function<? super T, ? extends K> keyMapper) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().collect(Collectors.groupingBy(keyMapper));

    }
    /**
     * toset
     */
    public static <T> Set<T> toSet(Collection<T> collection) {
        return new HashSet<>(Optional.ofNullable(collection).orElse(Lists.newArrayList()));
    }


    /**
     * findFirst
     */
    public static <T> T findFirst(Collection<T> collection) {
        return Optional.ofNullable(collection).orElse(Lists.newArrayList()).stream().findFirst().orElse(null);
    }


    public static <T> long sumLong(Collection<T> collection, Function<? super T, Long> mapper) {
        return Optional.ofNullable(collection).orElseGet(Collections::emptyList) // 避免无效对象创建[7](@ref)
                .parallelStream()                   // 并行流提升性能[3](@ref)
                .mapToLong(element -> {
                    Long value = mapper.apply(element);
                    return value != null ? value : 0L; // 空值防御[1,5](@ref)
                }).sum(); // 自动处理原始类型累加[3,6](@ref)
    }


    public static <T> int sumInt(Collection<T> collection, Function<? super T, Integer> mapper) {
        return Optional.ofNullable(collection).orElseGet(Collections::emptyList) // 避免无效对象创建[7](@ref)
                .parallelStream()                   // 并行流提升性能[3](@ref)
                .mapToInt(element -> {
                    Integer value = mapper.apply(element);
                    return value != null ? value : 0; // 空值防御[1,5](@ref)
                }).sum(); // 自动处理原始类型累加[3,6](@ref)
    }

    /**
     * distinctByKey
     */
    public static <T> List<T> distinctByKey(Collection<T> collection, Function<? super T, ?> keyExtractor) {
        Set<Object> set = new HashSet<>();
        List<T> result = new ArrayList<>();
        for(T t : collection) {
            Object apply = keyExtractor.apply(t);
            if (set.add(apply)) {
                result.add(t);
            }
        }
        return result;
    }

    /**
     * set2List
     */
    public static <T> List<T> set2List(Set<T> set) {
        return new ArrayList<>(Optional.ofNullable(set).orElse(new HashSet<>()));
    }

    /**
     * toStr
     */
    public static List<String> toStr(Collection<?> collection) {
        return Optional.ofNullable(collection)
                       .orElse(Lists.newArrayList())
                       .stream()
                       .map(Object::toString)
                       .collect(Collectors.toList());
    }

    /**
     * toLong
     */
    public static List<Long> toLong(Collection<?> collection) {
        return Optional.ofNullable(collection)
                       .orElse(Lists.newArrayList())
                       .stream()
                       .map(i->Long.valueOf(i.toString()))
                       .collect(Collectors.toList());
    }

}
