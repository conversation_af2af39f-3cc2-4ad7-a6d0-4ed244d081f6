package com.datatech.slgzt.model.security;

import lombok.Data;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-04-29 16:34
 **/
@Data
public class SecurityRuleModel {

    /**
     * 修改规则时必传
     */
    private String ruleId;

    /**
     * 访问规则方向：egress:出,ingress:进
     */
    private String direction;

    /**
     * 授权策略：拒绝-0/允许-1
     */
    private Integer accessStatus;

    /**
     * 优先级：访问规则的优先级，数字越小优先级越高
     */
    private Integer priority;

    /**
     * 协议类型：TCP/UDP/ICMP
     */
    private String protocol;

    /**
     * 端口范围
     */
    private String portRange;

    /**
     * 授权对象：输入CIDR信息，可以是IP或者网段，多个内容使用逗号分割。
     * 当入方向时，授权对象表示源IP，当出方向时，授权对象表示目的IP
     */
    private String accreditIp;

    private String accessIp;

    /**
     * 描述
     */
    private String description;

}
