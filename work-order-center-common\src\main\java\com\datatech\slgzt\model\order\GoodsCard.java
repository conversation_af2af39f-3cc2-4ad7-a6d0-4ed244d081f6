package com.datatech.slgzt.model.order;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 商品暂存数据对象
 *
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/12/30
 */


@Data
@Accessors(chain = true)
public class GoodsCard implements Serializable {

    /**
     * 暂存记录id，修改时不能为空
     */
    private Long id;

    /**
     * 商品类型 ecs：云主机, evs：云硬盘, gcs：GPU云主机, obs：对象存储, slb：负载均衡, nat：NAT网关, base：基础信息
     */
    private String goodsType;

    /**
     * 商品相关json格式数据，一个json表示一个商品数据
     */
    private Object orderJson;

}

