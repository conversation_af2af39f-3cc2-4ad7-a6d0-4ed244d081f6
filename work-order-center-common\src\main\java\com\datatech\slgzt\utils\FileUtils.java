package com.datatech.slgzt.utils;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.datatech.slgzt.annotation.ExcelExportHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


import java.io.*;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName FileUtils
 * @Description 文件工具类（现有的工具类不满足要求）
 * <AUTHOR>
 * @Date 2024/3/21 14:21
 */
@Component
@Slf4j
public class FileUtils {

    /**
     * 导出数据处理
     *
     * @param list   数据集
     * @param cls    数据对应实体类
     * @return ExcelWriter
     */
    public static void doExport(List<?> list, Class<?> cls, String exportFilePath) {
        //先判断文件是否存在，存在的话，先删除，不然导出会报错
        File file = new File(exportFilePath);
        if (file.exists()) {
            log.info("文件已删除：{}", file.delete());
        }
        ExcelWriter writer = ExcelUtil.getBigWriter(exportFilePath);
//        HSSFSheet tempSheet = (HSSFSheet) writer.getSheet();
//        tempSheet.setRandomAccessWindowSize(-1);
        if (Objects.nonNull(cls)) {
            addHeaderAlias(writer, cls);
        }
        writer.setOnlyAlias(true);
        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(list, true);
        writer.close();
    }

    /**
     * 根据注解属性，构造导出文件的列名
     *
     * @param writer ExcelWriter
     * @param cls    类属性
     */
    public static void addHeaderAlias(ExcelWriter writer, Class<?> cls) {
        List<Field> fields = new ArrayList<>();
        Class<?> superclass = cls.getSuperclass();
        if (superclass != null) {
            fields.addAll(Arrays.asList(superclass.getDeclaredFields()));
        }
        fields.addAll(Arrays.asList(cls.getDeclaredFields()));
        for (Field field : fields) {
            ExcelExportHeader header = field.getAnnotation(ExcelExportHeader.class);
            if (header != null) {
                writer.addHeaderAlias(field.getName(), header.value());
            }
        }
    }

    /**
     * 浏览器下载文件
     *
     * @param response   response
     * @param exportPath 文件路径
     * @param fileName   文件名
     */


    /**
     * 下载文件，返回文件流给前端
     */
    //public static void downloadFileStream(HttpServletResponse httpServletResponse, File file) {
    //    httpServletResponse.setContentType("application/octet-stream");
    //    httpServletResponse.setCharacterEncoding("utf-8");
    //    String fileName = file.getName();
    //    httpServletResponse.setContentLength((int) file.length());
    //    httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + fileName);
    //    httpServletResponse.setHeader("filename", fileName);
    //    try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file))) {
    //        byte[] buff = new byte[1024];
    //        OutputStream os = httpServletResponse.getOutputStream();
    //        int i;
    //        while ((i = bis.read(buff)) != -1) {
    //            os.write(buff, 0, i);
    //            os.flush();
    //        }
    //    } catch (Exception e) {
    //        log.error("Exception:{}", e.getMessage());
    //    }
    //}
}
