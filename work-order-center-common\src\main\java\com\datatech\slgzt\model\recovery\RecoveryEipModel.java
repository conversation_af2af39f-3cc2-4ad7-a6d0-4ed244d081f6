package com.datatech.slgzt.model.recovery;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 13:55:56
 */
@Data
public class RecoveryEipModel extends BaseReconveryProductModel {

    //eipId
    private String eipId;

    //eip
    @ExcelExportHeader(value = "弹性公网IP")
    private String eip;

    @ExcelExportHeader(value = "弹性公网名称")
    private String eipName;

    //带宽大小
    @ExcelExportHeader(value = "带宽大小")
    private String bandwidth;

    //数据盘挂载VmId
    private String vmId;

    //数据盘挂载VmName
    private String vmName;

    /**
     * 关联设备id
     */
    private String relatedDeviceId;
    /**
     * 关联设备名称
     */
    private String relatedDeviceName;
    /**
     * 关联设备类型
     */
    private String relatedDeviceType;



}
