package com.datatech.slgzt.model.file;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 上传文件模型
 *
 * <AUTHOR>
 * @date 2025年 03月03日 14:07:43
 */
@Data
@Accessors(chain = true)
public class UploadFileModel implements Serializable {

    /**
     * 文件id
     */
    private String id;

    private String fileId;

    /**
     * 创建者
     */
    private Long createdBy;

    private String targetName;

    /**
     * 文件编码
     */
    private String fileCode;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件后缀
     */
    private String suffix;
}
