package com.datatech.slgzt.impl.service.order;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ApprovalTimeEnum;
import com.datatech.slgzt.enums.GlobalExceptionEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.resource.ResourceTableResVO;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.service.ResourceDetailService;
import com.datatech.slgzt.service.network.NetworkMessageService;
import com.datatech.slgzt.service.network.VpcMessageService;
import com.datatech.slgzt.service.order.RecoveryWorkOrderTempSaveService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RKeys;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RecoveryWorkOrderTempSaveServiceImpl implements RecoveryWorkOrderTempSaveService {

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private VpcMessageService vpcMessageService;

    @Resource
    private NetworkMessageService networkMessageService;

    @Resource
    private ResourceDetailService resourceDetailService;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    private final String RESOURCE_KEY = "recovery_workOrder_temp_save:";

    @Override
    public Long handleCreate(String type, Long businessSystemId, Long userId, String goodsId, Boolean syncRecovery) {
        String redisKey = generateKey(type, userId);
        long id = ThreadLocalRandom.current().nextLong(1_000_000_000L, 10_000_000_000L);
        RMap<Long, JSONObject> resourceMap = redissonClient.getMap(redisKey);
        Collection<JSONObject> values = resourceMap.values();
        Map<String, Long> map = new HashMap<>();
        List<JSONObject> resourceList = new ArrayList<>();
        for (Object value : values) {
            JSONObject jsonObject = JSONObject.parseObject((String) value);
            String resourceId = jsonObject.getString("goodsId");
            map.putIfAbsent(resourceId, jsonObject.getLong("id"));
        }

        if (map.containsKey(goodsId)) {
            return map.get(goodsId);
        }
        JSONObject data = new JSONObject();
        data.put("id", id);
        data.put("goodsType", type);
        data.put("businessSystemId", businessSystemId);
        data.put("goodsId", goodsId);
        data.put("syncRecovery", syncRecovery);
        resourceMap.put(id, data);
        return id;
    }

    @Override
    public Long handleCreateBase(String type, Long userId, JSONObject orderJson) {
        String redisKey = generateKey(type, userId);
//        long id = IdUtil.getSnowflake().nextId();
//        id = Long.parseLong(String.valueOf(id).substring(0, 8));
        long id = ThreadLocalRandom.current().nextLong(1_000_000_000L, 10_000_000_000L);
        RMap<Long, JSONObject> resourceMap = redissonClient.getMap(redisKey);
        JSONObject data = new JSONObject();
        data.put("id", id);
        data.put("goodsType", type);
        data.put("orderJson", orderJson);
        resourceMap.put(id, data);
        return id;
    }

    @Override
    public void handleUpdate(Long id, String type, Long userId, JSONObject orderJson) {
        String redisKey = generateKey(type, userId);
        RMap<Long, JSONObject> resourceMap = redissonClient.getMap(redisKey);
        JSONObject data = new JSONObject();
        data.put("id", id);
        data.put("goodsType", type);
        data.put("orderJson", orderJson);
        resourceMap.put(id, data);

    }

    @Override
    public void handleDelete(List<String> idList, String type, Long userId) {
        RMap<Long, JSONObject> resourceMap = redissonClient.getMap(generateKey(type, userId));
        idList.forEach(resourceMap::remove);
        if (resourceMap.isEmpty()) {
            // 判断下当前用户下是否还有产品，没有的话则需要将baseList删除掉
            String baseKey = generateKey("base", userId);
            if (StringUtils.isEmpty(baseKey)) {
                return;
            }

            Iterable<String> matchedKeys = queryCacheKeyData(userId);
            List<String> keyList = new ArrayList<>();
            matchedKeys.forEach(key -> {
                if (!baseKey.equalsIgnoreCase(key)) {
                    keyList.add(key);
                }
            });
            if (keyList.isEmpty()) {
                RKeys keys = redissonClient.getKeys();
                keys.delete(baseKey);
            }
        }
    }

    /**
     * 删除用户下所有的临时数据
     */
    @Override
    public void handleDeleteAll(Long userId) {
        RKeys keys = redissonClient.getKeys();
        Iterable<String> matchedKeys = queryCacheKeyData(userId);
        matchedKeys.forEach(keys::delete);
    }

    private Iterable<String> queryCacheKeyData(Long userId) {
        RKeys keys = redissonClient.getKeys();
        Joiner joiner = Joiner.on(":");
        String pattern = joiner.join(RESOURCE_KEY, userId, "*");
        return keys.getKeysByPattern(pattern);
    }

    /**
     * 获取当前用户的暂存对象
     *
     * @param userId
     */
    @Override
    public JSONObject getTempSave(Long userId) {
        Joiner on = Joiner.on(":");
        String key = on.join(RESOURCE_KEY, userId, "*");
        RKeys keys = redissonClient.getKeys();
        Iterable<String> matchedKeys = keys.getKeysByPattern(key);
        JSONObject result = new JSONObject();
        matchedKeys.forEach(item -> {
            RMap<Long, String> resourceMap = redissonClient.getMap(item);
            //属性名称用 key最后一个冒号后面的字符串
            int lastColonIndex = item.lastIndexOf(':');
            //这里取出来的是 ecs 这种 要加上List
            String typeName = (lastColonIndex == -1) ? Strings.nullToEmpty(item) : item.substring(lastColonIndex + 1);
            typeName = typeName + "List";
            //数据部分装配到List里
            List<JSONObject> list = new ArrayList<>();
            for (String value : resourceMap.values()) {
                JSONObject jsonObject = JSON.parseObject(value);
                // businessSysName
                Long businessSystemId = jsonObject.getLong("businessSystemId");
                String systemName = resourceDetailService.selectByBusinessSystemId(businessSystemId);
                jsonObject.put("businessSysName", systemName);
                // 保留同步回收标识
                if (jsonObject.containsKey("syncRecovery")) {
                    Boolean syncRecovery = jsonObject.getBoolean("syncRecovery");
                    jsonObject.put("syncRecovery", syncRecovery != null ? syncRecovery : false);
                }
                list.add(jsonObject);
            }
            result.put(typeName, list);
        });
        return result;
    }

    @Override
    public void canRecovery(String type, List<String> goodsIdList) {
        // 云主机和gpu云主机时校验设备的开关机状态，就有运行中的才可以添加回收，其他类型需要校验回收状态时未回收的
        List<String> types = Arrays.asList(ProductTypeEnum.ECS.getCode(), ProductTypeEnum.GCS.getCode(),ProductTypeEnum.MYSQL.getCode(),ProductTypeEnum.REDIS.getCode());
        if (types.contains(type)) {
            List<String> deviceStatusList = resourceDetailService.selectDeviceStatusByIds(goodsIdList);
            Precondition.checkArgument(deviceStatusList, "选择的资源数据已被删除，请联系管理员");
            long count = deviceStatusList.stream()
                                         .filter(deviceStatus -> !"RUNING".equalsIgnoreCase(deviceStatus))
                                         .count();
            Precondition.checkArgument(count <= 0, "选择的回收资源设备存在非运行状态，当前回收操作失败");
            List<Long> ids = new ArrayList<>();
            goodsIdList.forEach(id -> ids.add(Long.valueOf(id)));
            List<ResourceDetailDTO> list = resourceDetailManager.list(new ResourceDetailQuery().setIds(ids));
            long l = list.stream().filter(detail -> StringUtils.isNotBlank(detail.getBackupId())).count();
            Precondition.checkArgument(l <= 0, "选择的回收资源存在未解绑的备份策略，当前回收操作失败");
            List<String> vmIds = list.stream().map(ResourceDetailDTO::getDeviceId).collect(Collectors.toList());
            vmIds.forEach(vmId -> {
                List<ResourceDetailDTO> dtoList = resourceDetailManager.list(new ResourceDetailQuery().setVmId(vmId).setType("evs"));
                long j = dtoList.stream().filter(detail -> StringUtils.isNotBlank(detail.getBackupId())).count();
                Precondition.checkArgument(j <= 0, "选择的回收资源挂载的云硬盘存在未解绑的备份策略，当前回收操作失败");
            });
        }

        if (ProductTypeEnum.EVS.getCode().equalsIgnoreCase(type)) {
            List<Long> ids = new ArrayList<>();
            goodsIdList.forEach(id -> ids.add(Long.valueOf(id)));
            List<ResourceDetailDTO> list = resourceDetailManager.list(new ResourceDetailQuery().setIds(ids));
            long l = list.stream().filter(detail -> StringUtils.isNotBlank(detail.getBackupId())).count();
            Precondition.checkArgument(l <= 0, "选择的回收资源存在未解绑的备份策略，当前回收操作失败");
        }

        if (ProductTypeEnum.VPC.getCode().equalsIgnoreCase(type)) {
            int count = vpcMessageService.selectRecoveryStatusByIds(goodsIdList, RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
            Precondition.checkArgument(count == goodsIdList.size(), "选择的vpc回收中存在正在回收的数据或绑定的资源，当前回收操作失败");
            // 校验vpc是否被其他未回收的资源使用，是则不能进行回收，ecs/gcs/slb/nat可以绑定网络
            checkVpcIsUse(goodsIdList, type);
            return;
        }

        if (ProductTypeEnum.NETWORK.getCode().equalsIgnoreCase(type)) {
            int count = networkMessageService.selectRecoveryStatusByIds(goodsIdList, RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
            Precondition.checkArgument(count == goodsIdList.size(), "选择的network回收中存在正在回收的数据或绑定的资源，当前回收操作失败");
            // 校验网络是否被其他未回收的资源使用，是则不能进行回收
            checkVpcIsUse(goodsIdList, type);
            return;
        }

        if (ProductTypeEnum.BACKUP.getCode().equalsIgnoreCase(type)) {
            List<Long> ids = new ArrayList<>();
            goodsIdList.forEach(id -> ids.add(Long.valueOf(id)));
            List<ResourceDetailDTO> list = resourceDetailManager.list(new ResourceDetailQuery().setIds(ids));
            Precondition.checkArgument(list, "选择的资源数据已被删除，请联系管理员");
            List<String> relatedDevices = list.stream().map(ResourceDetailDTO::getRelatedDeviceId).collect(Collectors.toList());
            List<ResourceDetailDTO> relatedDetails = resourceDetailManager.list(new ResourceDetailQuery().setDeviceIds(relatedDevices));
            Map<String, ResourceDetailDTO> map = new HashMap<>();
            if (CollectionUtil.isNotEmpty(relatedDetails)) {
                map = relatedDetails.stream()
                        .collect(Collectors.toMap(
                                ResourceDetailDTO::getDeviceId,
                                dto -> dto
                        ));
            }
            Map<String, ResourceDetailDTO> finalMap = map;
            long count = list.stream()
                    .filter(detail -> StringUtils.isNotBlank(detail.getRelatedDeviceId()) && Objects.nonNull(finalMap.get(detail.getRelatedDeviceId())))
                    .count();
            Precondition.checkArgument(count <= 0, "选择的回收资源存在未解绑的资源，当前回收操作失败");
        }

        int count = resourceDetailService.selectRecoveryStatusByIds(goodsIdList, RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
        Precondition.checkArgument(count == goodsIdList.size(), "选择的资源回收中存在正在回收的数据或绑定的资源，当前回收操作失败");
    }

    private void checkVpcIsUse(List<String> goodsIdList, String type) {
        List<ResourceDetailDTO> detailDTOS = resourceDetailService.selectResourceByVpcIds(goodsIdList);
        if (CollectionUtil.isNotEmpty(detailDTOS)) {
            Map<String, List<Long>> resultMap = detailDTOS.stream()
                                                          .collect(Collectors.groupingBy(
                                                                  ResourceDetailDTO::getVpcId,
                                                                  Collectors.mapping(ResourceDetailDTO::getId, Collectors.toList())
                                                          ));
            log.info("回收资源类型：{},回收失败，回收的记录被其他资源:{},绑定或者回收", type, JSON.toJSONString(resultMap));
            Precondition.checkArgument(CollectionUtil.isEmpty(detailDTOS), "选择的vpc回收中存在正在回收的数据或绑定的资源，当前回收操作失败");
        }
    }

    public void checkVpcIsUse(List<String> goodsIdList) {
        checkVpcIsUse(goodsIdList, ProductTypeEnum.VPC.getCode());
    }

    /**
     * 生成key
     */
    public String generateKey(String type, Long userId) {
        Joiner joiner = Joiner.on(":");
        return joiner.join(RESOURCE_KEY, userId, type);
    }

    /**
     * 解析前端key
     * 前端一般入参类似 ecsList gcsList
     * 把 ecsList 解析成 ecs
     */
    @Override
    public String parseKey(String key) {
        //先校验下
        Precondition.checkArgument(key.endsWith("List"), "key不合法");
        return key.substring(0, key.length() - 4);
    }

    @Override
    public Integer getTempSaveCount(Long userId) {
        Joiner on = Joiner.on(":");
        String key = on.join(RESOURCE_KEY, userId, "*");
        RKeys keys = redissonClient.getKeys();
        Iterable<String> matchedKeys = keys.getKeysByPattern(key);
        //查询所有的key 获取key下面的数据量
        int count = 0;
        for (String item : matchedKeys) {
            //如果 key包含base 则不计数
            if (item.contains("base")) {
                continue;
            }
            RMap<Long, String> resourceMap = redissonClient.getMap(item);
            count += resourceMap.size();
        }
        return count;
    }

    @Override
    public Long parseBusinessSystemId(Long userId, String type, List<String> goodsIds) {
        List<Long> businessIds = new ArrayList<>();
        switch (type) {
            case "ecs":
            case "gcs":
            case "mysql":
            case "redis":
            case "evs":
            case "eip":
            case "obs":
            case "slb":
            case "nat":
            case "backup":
            case "vpn":
            case "nas":
            case "pm":
            case "kafka":
            case "flink":
            case "es":
                businessIds = resourceDetailService.selectBusinessIdByGoodsIds(goodsIds);
                break;
            case "vpc":
                businessIds = vpcMessageService.selectBusinessIdByVpcIds(goodsIds);
                break;
            case "network":
                businessIds = networkMessageService.selectBusinessIdByNetworkIds(goodsIds);
                break;
            default:
                break;
        }
        businessIds = businessIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(businessIds)) {
            log.error("添加回收购物车时传入的产品类型：{}，资源id：{}，在数据库中找不到对应的业务系统，请确认", type, goodsIds);
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), "回收时添加的资源在数据库中找不到对应的业务系统，请联系管理员");
        }

        if (businessIds.size() > 1) {
            log.error("添加回收购物车时传入的产品类型：{}中的资源id：{}并不是归属相同的业务系统：{}，添加回收购物车失败", type, goodsIds, businessIds);
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), "回收时添加的资源不是归属相同的业务系统，请联系管理员");
        }

        String redisKey = generateKey("*", userId);
        List<JSONObject> resourceMap = new ArrayList<>();
        Iterable<String> keys = redissonClient.getKeys().getKeysByPattern(redisKey);
        // 遍历键并获取对应的值
        for (String key : keys) {
            RMap<Object, JSONObject> map = redissonClient.getMap(key);
            resourceMap.addAll(map.values());
        }

        Long businessSystemId = businessIds.get(0);
        if (CollectionUtil.isEmpty(resourceMap)) {
            // 先前没有添加回收购物车，直接将业务系统返回
            return businessSystemId;
        }

        Set<Long> systemIdSet = new HashSet<>();
        for (Object value : resourceMap) {
            JSONObject jsonObject = JSONObject.parseObject((String) value);
            Long systemId = jsonObject.getLong("businessSystemId");
            systemIdSet.add(systemId);
        }

        systemIdSet = systemIdSet.stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(systemIdSet) && !systemIdSet.contains(businessSystemId)) {
            log.error("添加回收购物车时传入的产品类型：{}中的资源id：{}归属的业务系统：{}和之前添加的业务系统：{}不是同一个，添加回收购物车失败", type, goodsIds, businessSystemId, systemIdSet);
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), "回收时添加的资源和先前添加的业务系统不同，添加回收失败，请联系管理员");
        }

        return businessSystemId;
    }

    private void convertCloudPlatform(List<ResourceDetailDTO> resourceDetails) {
        if (CollectionUtil.isEmpty(resourceDetails)) {
            return;
        }
        for (ResourceDetailDTO dto : resourceDetails) {
            String domainName = dto.getDomainName();
            if (StringUtils.isNotEmpty(domainName)) {
                dto.setCloudPlatform(domainName);
            }
            ApprovalTimeEnum approvalTimeEnum = ApprovalTimeEnum.getByCode(dto.getApplyTime());
            if (approvalTimeEnum != null) {
                dto.setApplyTime(approvalTimeEnum.getName());
            }
            dto.setDeviceStatus(deviceStatus(dto.getDeviceStatus()));
            dto.setRelatedDeviceType(ProductTypeEnum.getByCode(dto.getRelatedDeviceType()).getDesc());

        }
    }

    /**
     * 有些资源设备状态存了其他意义的值，需要按原先值传递
     * @param esList
     */
    private void convertDeviceStatus(List<ResourceDetailDTO> esList) {
        Map<Long, String> resultMap = esList.stream()
                .collect(Collectors.toMap(
                        ResourceDetailDTO::getId,      // Key Mapper
                        ResourceDetailDTO::getDeviceStatus, // Value Mapper
                        (oldValue, newValue) -> oldValue // 解决重复key的合并策略（保留旧值）
                ));

    }

    @Override
    public void listProductByType(String type, JSONArray jsonArray, ResourceTableResVO resVO) {
        Map<String, Long> resourceMap = new HashMap<>();
        Map<String, Boolean> syncRecoveryMap = new HashMap<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject objData = jsonArray.getJSONObject(i);
            String goodsId = objData.getString("goodsId");
            resourceMap.put(goodsId, objData.getLong("id"));
            // 记录每个商品的同步回收状态
            Boolean syncRecovery = objData.getBoolean("syncRecovery");
            syncRecoveryMap.put(goodsId, syncRecovery != null ? syncRecovery : false);
        }

        Precondition.checkArgument(resourceMap, "查询回收产品列表失败，请检查传入参数");
        // 获取所有值
        Set<String> goodsIds = resourceMap.keySet();
        List<String> goodsIdList = new ArrayList<>(goodsIds);

        switch (type) {
            case "ecs":
                List<ResourceDetailDTO> ecsList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(ecsList);
                // 设置同步回收标识
                ecsList.forEach(ecs -> {
                    Boolean syncRecovery = syncRecoveryMap.get(ecs.getId().toString());
                    ecs.setSyncRecovery(syncRecovery);
                });
                resVO.setEcsList(ecsList);
                break;
            case "gcs":
                List<ResourceDetailDTO> gcsList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(gcsList);
                // 设置同步回收标识
                gcsList.forEach(gcs -> {
                    Boolean syncRecovery = syncRecoveryMap.get(gcs.getId().toString());
                    gcs.setSyncRecovery(syncRecovery);
                });
                resVO.setGcsList(gcsList);
                break;
            case "mysql":
                List<ResourceDetailDTO> mysqlList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(mysqlList);
                // 设置同步回收标识
                mysqlList.forEach(gcs -> {
                    Boolean syncRecovery = syncRecoveryMap.get(gcs.getId().toString());
                    gcs.setSyncRecovery(syncRecovery);
                });
                resVO.setMysqlList(mysqlList);
                break;
            case "redis":
                List<ResourceDetailDTO> redisList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(redisList);
                // 设置同步回收标识
                redisList.forEach(gcs -> {
                    Boolean syncRecovery = syncRecoveryMap.get(gcs.getId().toString());
                    gcs.setSyncRecovery(syncRecovery);
                });
                resVO.setRedisList(redisList);
                break;
            case "eip":
                List<ResourceDetailDTO> eipList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(eipList);
                resVO.setEipList(eipList);
                break;
            case "evs":
                List<ResourceDetailDTO> evsList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(evsList);
                resVO.setEvsList(evsList);
                break;
            case "obs":
                List<ResourceDetailDTO> obsList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(obsList);
                resVO.setObsList(obsList);
                break;
            case "slb":
                List<ResourceDetailDTO> slbList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(slbList);
                // 设置同步回收标识
                slbList.forEach(gcs -> {
                    Boolean syncRecovery = syncRecoveryMap.get(gcs.getId().toString());
                    gcs.setSyncRecovery(syncRecovery);
                });
                resVO.setSlbList(slbList);
                break;
            case "nat":
                List<ResourceDetailDTO> natList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(natList);
                // 设置同步回收标识
                natList.forEach(gcs -> {
                    Boolean syncRecovery = syncRecoveryMap.get(gcs.getId().toString());
                    gcs.setSyncRecovery(syncRecovery);
                });
                resVO.setNatList(natList);
                break;
            case "vpc":
                List<VpcOrderResult> vpcList = vpcMessageService.selectByIds(goodsIdList, resourceMap);
                resVO.setVpcList(vpcList);
                break;
            case "network":
                List<NetworkOrderResult> networkList = networkMessageService.selectByIds(goodsIdList, resourceMap);
                resVO.setNetworkList(networkList);
                break;
            case "backup":
                List<ResourceDetailDTO> backupList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(backupList);
                resVO.setBackupList(backupList);
                break;
            case "vpn":
                List<ResourceDetailDTO> vpnList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(vpnList);
                resVO.setVpnList(vpnList);
                break;
            case "nas":
                List<ResourceDetailDTO> nasList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(nasList);
                resVO.setNasList(nasList);
                break;
            case "pm":
                List<ResourceDetailDTO> pmList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(pmList);
                resVO.setPmList(pmList);
                break;
            case "kafka":
                List<ResourceDetailDTO> kafkaList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                Map<Long, String> kafkaResultMap = kafkaList.stream()
                        .collect(Collectors.toMap(
                                ResourceDetailDTO::getId,      // Key Mapper
                                ResourceDetailDTO::getDeviceStatus, // Value Mapper
                                (oldValue, newValue) -> oldValue // 解决重复key的合并策略（保留旧值）
                        ));
                convertCloudPlatform(kafkaList);
                kafkaList.forEach(dto -> {
                    String status = kafkaResultMap.get(dto.getId());
                    if (status != null) {
                        dto.setDeviceStatus(status);
                    }
                });
                resVO.setKafkaList(kafkaList);
                break;
            case "flink":
                List<ResourceDetailDTO> flinkList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                convertCloudPlatform(flinkList);
                resVO.setFlinkList(flinkList);
                break;
            case "es":
                List<ResourceDetailDTO> esList = resourceDetailService.selectByGoodsIds(goodsIdList, resourceMap);
                Map<Long, String> esResultMap = esList.stream()
                        .collect(Collectors.toMap(
                                ResourceDetailDTO::getId,      // Key Mapper
                                ResourceDetailDTO::getDeviceStatus, // Value Mapper
                                (oldValue, newValue) -> oldValue // 解决重复key的合并策略（保留旧值）
                        ));
                convertCloudPlatform(esList);
                esList.forEach(dto -> {
                    String status = esResultMap.get(dto.getId());
                    if (status != null) {
                        dto.setDeviceStatus(status);
                    }
                });
                resVO.setEsList(esList);
                break;
            default:
                break;
        }
    }

    private String deviceStatus(String deviceStatus) {
        if (ObjNullUtils.isNull(deviceStatus)) {
            return null;
        }
        if (deviceStatus.equals("RUNING")) {
            return "运行中";
        }
        if (deviceStatus.equals("STARTING")) {
            return "启动中";
        }
        if (deviceStatus.equals("ERROR")) {
            return "异常";
        }
        if (deviceStatus.equals("RESTARTING")) {
            return "重启中";
        }
        if (deviceStatus.equals("STOPED")) {
            return "已停止";
        }
        if (deviceStatus.equals("DELETED")) {
            return "已删除";
        }
        return "运行中";
    }
}
