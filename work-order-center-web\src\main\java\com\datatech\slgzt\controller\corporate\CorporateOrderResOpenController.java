package com.datatech.slgzt.controller.corporate;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.BatchRestartModel;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.KafkaMessage;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.nostander.BackupModel;
import com.datatech.slgzt.model.nostander.MysqlV2Model;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.req.res.OrderStatusNoticeReq;
import com.datatech.slgzt.service.CmdbResourceCenterService;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.corporate.CorporateOrderService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.datatech.slgzt.enums.SourceTypeEnum.CORPORATE;

@Slf4j
@RestController
@RequestMapping("/corporateOrderResOpen")
public class CorporateOrderResOpenController {

    private static final String BATCH_RESTART_TOPIC = "woc_corporate_batch_restart_topic";

    @Resource
    private CorporateOrderManager orderManager;

    @Resource
    private CorporateOrderService orderService;

    @Resource
    private CorporateOrderProductManager productManager;

    @Resource
    private DgRecoveryOrderProductManager recoveryProductManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;
    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Resource
    private CmdbReportService cmdbReportService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CmdbResourceCenterService cmdbResourceCenterService;
    @Resource
    private VnicManager vnicManager;
    @Resource
    private StaticProductStockManager staticProductStockManager;

    /**
     * 回调接口
     * 工单中心->调用->编排中心
     * 编排中心->调用->工单中心 这一步的回调方法
     * 返回结果 包括所有产品 这边对应PRODUCT表
     * 改变状态和消息即可
     *
     * @param req
     * @return
     */
    @PostMapping("/layoutTaskNotify")
    public CommonResult<Void> layoutTaskNotify(@RequestBody @Validated OrderStatusNoticeReq req) {
        //订购类型
        log.info("corporate layoutTaskNotify start req:{}", JSONObject.toJSONString(req));
        String type = req.getType();
        //1是产品主任务的回调  2是子产品的回调
        Integer orderType = req.getOrderType();
        switch (OrderTypeEnum.getByCode(type)) {
            case SUBSCRIBE:
                subscribeLayout(orderType, req);
                break;
            case UNSUBSCRIBE:
                unsubscribeLayout(orderType, req);
                break;
            case MODIFY:
                modifyLayout(orderType, req);
                break;
        }
        return CommonResult.build(1, 200, "回调成功", null);
    }

    /**
     * 资源开通回调
     */
    private void subscribeLayout(Integer orderType, OrderStatusNoticeReq req) {
        if (orderType == 1) {
            CorporateOrderProductDTO productDTO = productManager.getBySubOrderId(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(productDTO, "找不到对应产品");
            CorporateOrderDTO orderDTO = orderManager.getById(productDTO.getOrderId());
            Precondition.checkArgument(orderDTO, "找不到对应工单");
            //更新对应的状态
            productDTO.setMessage(req.getMessage());
            productDTO.setOpenStatus(ResOpenEnum.adaptTaskCenterResult(req.getHandleResult()).getCode());
            productManager.update(productDTO);
//            obsOpenTaskManager.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), productDTO.getId());
            // 如果成功，则重启流程，继续往下执行
            if (req.getHandleResult().equals(1)) {
                kafkaTemplate.send(BATCH_RESTART_TOPIC, orderDTO.getId(), KafkaMessage.of(new BatchRestartModel()
                        .setJobExecutionId(orderDTO.getJobExecutionId())
                        // 已更新product，不需要它更新
                        .setRestartOnly(true)));
            } else {
                //失败了发送短信通知
                orderService.sendFailSms(productDTO.getId());
            }
        }
        if (orderType == 2) {
            CorporateOrderProductDTO productDTO = productManager.getById(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(productDTO, "找不到对应产品");
            CorporateOrderDTO orderDTO = orderManager.getById(productDTO.getOrderId());
            Precondition.checkArgument(orderDTO, "找不到对应工单");
            //备份策略需要取resourceId，不在主产品更新
            if (0 == productDTO.getParentProductId()
                    && !ProductTypeEnum.BACKUP.getCode().equals(productDTO.getProductType())
                    && !ProductTypeEnum.RDS_MYSQL.getCode().equals(productDTO.getProductType())) {
                log.info("该产品是主产品，由主任务来更新状态");
                return;
            }
            //更新对应的状态
            productDTO.setMessage(req.getMessage());
            productDTO.setOpenStatus(ResOpenEnum.adaptTaskCenterResult(req.getHandleResult()).getCode());
            productManager.update(productDTO);
            if (ProductTypeEnum.RDS_MYSQL.getCode().equals(productDTO.getProductType()) && 1 == req.getHandleResult()) {
//                saveRdsMysqlResource(productDTO, orderDTO, req.getResourceId());
                // 如果成功，则重启流程，继续往下执行
                if (req.getHandleResult().equals(1)) {
                    kafkaTemplate.send(BATCH_RESTART_TOPIC, orderDTO.getId(), KafkaMessage.of(new BatchRestartModel()
                            .setJobExecutionId(orderDTO.getJobExecutionId())
                            // 已更新product，不需要它更新
                            .setRestartOnly(true)));
                } else {
                    //失败了发送短信通知
                    orderService.sendFailSms(productDTO.getId());
                }
            }
            //如果是备份策略，构造资源入库
            if (ProductTypeEnum.BACKUP.getCode().equals(productDTO.getProductType()) && 1 == req.getHandleResult()) {
                saveBackupResource(productDTO, orderDTO, req.getResourceId());
                // 如果成功，则重启流程，继续往下执行
                if (req.getHandleResult().equals(1)) {
                    kafkaTemplate.send(BATCH_RESTART_TOPIC, orderDTO.getId(), KafkaMessage.of(new BatchRestartModel()
                            .setJobExecutionId(orderDTO.getJobExecutionId())
                            // 已更新product，不需要它更新
                            .setRestartOnly(true)));
                } else {
                    //失败了发送短信通知
                    orderService.sendFailSms(productDTO.getId());
                }
            }
        }
    }

    /**
     * 资源回收回调
     */
    private void unsubscribeLayout(Integer orderType, OrderStatusNoticeReq req) {
        if (orderType == 1) {
            DgRecoveryOrderProductDTO recoveryProductDTO = recoveryProductManager.getBySubOrderId(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(recoveryProductDTO, "找不到对应产品");
            recoveryProductDTO.setMessage(1 == req.getHandleResult() ? " " : req.getMessage());
            recoveryProductDTO.setRecoveryStatus(RecoveryStatusEnum.adaptTaskCenterResult(req.getHandleResult()).getType());
            recoveryProductDTO.setModifyTime(LocalDateTime.now());
            recoveryProductManager.update(recoveryProductDTO);
            //失败了不更新资源状态
            if (1 == req.getHandleResult()) {
                String productType = recoveryProductDTO.getProductType();
                if (ProductTypeEnum.ECS.getCode().equals(productType)
                        || ProductTypeEnum.GCS.getCode().equals(productType)
                        || ProductTypeEnum.MYSQL.getCode().equals(productType)
                        || ProductTypeEnum.REDIS.getCode().equals(productType)) {
                    ecsComplete(recoveryProductDTO);
                } else if (ProductTypeEnum.EIP.getCode().equals(productType)) {
                    eipComplete(recoveryProductDTO);
                } else if (ProductTypeEnum.SLB.getCode().equals(productType)
                        || ProductTypeEnum.NAT.getCode().equals(productType)) {
                    slbAndNatComplete(recoveryProductDTO);
                } else if (ProductTypeEnum.EVS.getCode().equals(productType)) {
                    evsComplete(recoveryProductDTO);
                } else if (ProductTypeEnum.VPN.getCode().equals(productType)) {
                    //如果是vmware的vpn，回收成功后塞一个标记到缓存中
                    ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
                    Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
                    if (DomainCodeEnum.PLF_PROV_MOC_ZJ_VMWARE.getCode().equals(resourceDetailDTO.getDomainCode())) {
                        RBucket<String> bucket = redissonClient.getBucket("vpn:recovery:" + resourceDetailDTO.getVpcId());
                        bucket.set("vpn:recovery:" + resourceDetailDTO.getVpcId(), 7, TimeUnit.DAYS);
                    }
                }
                cmdbReportService.deleteInstance(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
                cmdbResourceCenterService.delLevel3IpBindBusSys(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
                resourceDetailManager.deleteById(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
            }
        }
        if (orderType == 2) {
            DgRecoveryOrderProductDTO recoveryProductDTO = recoveryProductManager.getById(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(recoveryProductDTO, "找不到对应产品");
            if (0 == recoveryProductDTO.getParentProductId()) {
                log.info("该产品是主产品，由主任务来更新状态");
                return;
            }
            recoveryProductDTO.setMessage(req.getMessage());
            recoveryProductDTO.setRecoveryStatus(RecoveryStatusEnum.adaptTaskCenterResult(req.getHandleResult())
                    .getType());
            recoveryProductManager.update(recoveryProductDTO);
            if (1 == req.getHandleResult()) {
                //删除资源数据
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
                Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
                resourceDetailManager.deleteById(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
            }
        }
    }

    /**
     * 资源变更回调
     */
    private void modifyLayout(Integer orderType, OrderStatusNoticeReq req) {
//        ChangeTypeProductStatusEnum changeResult = ChangeTypeProductStatusEnum.adaptTaskCenterResult(req.getHandleResult());
//        ChangeWorkOrderProductDTO changeWorkOrderProductDTO = null;
//        if (orderType == 1) {
//            changeWorkOrderProductDTO = changeWorkOrderProductManager.getBySubOrderId(Long.valueOf(req.getOrderId()));
//            Precondition.checkArgument(changeWorkOrderProductDTO, "找不到对应产品");
//            changeWorkOrderProductDTO.setMessage(1 == req.getHandleResult() ? " " : req.getMessage());
//            changeWorkOrderProductDTO.setChangeStatus(changeResult.getCode());
//        }
//        if (orderType == 2) {
//            changeWorkOrderProductDTO = changeWorkOrderProductManager.getById(Long.valueOf(req.getOrderId()));
//            Precondition.checkArgument(changeWorkOrderProductDTO, "找不到对应产品");
//            if (0 == changeWorkOrderProductDTO.getParentProductId()) {
//                log.info("该产品是主产品，由主任务来更新状态");
//                return;
//            }
//            changeWorkOrderProductDTO.setMessage(req.getMessage());
//            changeWorkOrderProductDTO.setChangeStatus(String.valueOf(changeResult.getCode()));
//
//        }
//        if (changeWorkOrderProductDTO != null) {
//            changeWorkOrderProductManager.update(changeWorkOrderProductDTO);
//            if (changeResult == ChangeTypeProductStatusEnum.CHANGE_SUCCESS
//                    && (ProductTypeEnum.ECS.getCode().equals(changeWorkOrderProductDTO.getProductType())
//                    || ProductTypeEnum.GCS.getCode().equals(changeWorkOrderProductDTO.getProductType()))
//                    || ProductTypeEnum.MYSQL.getCode().equals(changeWorkOrderProductDTO.getProductType())
//                    || ProductTypeEnum.REDIS.getCode().equals(changeWorkOrderProductDTO.getProductType())) {
//                ResourceDetailDTO detailDTO = resourceDetailManager.getById(Long.valueOf(changeWorkOrderProductDTO.getResourceDetailId()));
//                changeWorkOrderService.tryStartEcs(detailDTO);
//            }
//            changeWorkOrderService.checkAndSendSMS(Long.valueOf(changeWorkOrderProductDTO.getResourceDetailId()));
//        }
    }

    /**
     * 备份策略资源入库
     */
    private void saveBackupResource(CorporateOrderProductDTO productDTO,
                                    CorporateOrderDTO orderDTO,
                                    String resourceId) {
        BackupModel backupModel = JSON.parseObject(productDTO.getPropertySnapshot(), BackupModel.class);
        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setType(ProductTypeEnum.BACKUP.getCode());
        resourceDetailDTO.setOrderId(orderDTO.getId());
        resourceDetailDTO.setOrderCode(orderDTO.getOrderCode());
        resourceDetailDTO.setBillId(backupModel.getBillId());
        resourceDetailDTO.setDeviceId(resourceId);
        resourceDetailDTO.setDeviceName(backupModel.getJobName());
        resourceDetailDTO.setTenantId(backupModel.getTenantId());
        resourceDetailDTO.setTenantName(orderDTO.getTenantName());
        resourceDetailDTO.setBusinessSysId(backupModel.getBusinessSystemId());
        resourceDetailDTO.setBusinessSysName(backupModel.getBusinessSystemName());
        resourceDetailDTO.setDomainCode(backupModel.getDomainCode());
        resourceDetailDTO.setDomainName(backupModel.getDomainName());
        resourceDetailDTO.setApplyUserName(orderDTO.getCreateByName());
        resourceDetailDTO.setResourcePoolId(String.valueOf(backupModel.getRegionId()));
        resourceDetailDTO.setResourcePoolCode(backupModel.getRegionCode());
        resourceDetailDTO.setResourcePoolName(backupModel.getRegionName());
        resourceDetailDTO.setBackupType(backupModel.getBackupType());
        resourceDetailDTO.setFrequency(backupModel.getFrequency());
        resourceDetailDTO.setDaysOfWeek(backupModel.getDaysOfWeek());
        resourceDetailDTO.setCreateTime(LocalDateTime.now());
        resourceDetailDTO.setResourceApplyTime(orderDTO.getCreateTime());
        resourceDetailDTO.setEffectiveTime(LocalDateTime.now());
        resourceDetailDTO.setSourceExtType(CORPORATE.getPrefix());
        resourceDetailManager.batchSaveResourceDetail(ListUtil.toList(resourceDetailDTO));
    }

    private void saveRdsMysqlResource(CorporateOrderProductDTO productDTO,
                                      CorporateOrderDTO orderDTO,
                                      String resourceId) {
        MysqlV2Model mysqlV2Model = JSON.parseObject(productDTO.getPropertySnapshot(), MysqlV2Model.class);
        ResourceDetailDTO resourceDetailDTO = new ResourceDetailDTO();
        resourceDetailDTO.setId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
        resourceDetailDTO.setType(ProductTypeEnum.RDS_MYSQL.getCode());
        resourceDetailDTO.setOrderId(orderDTO.getId());
        resourceDetailDTO.setOrderCode(orderDTO.getOrderCode());
        resourceDetailDTO.setBillId(mysqlV2Model.getBillId());
        resourceDetailDTO.setDeviceId(resourceId);
        resourceDetailDTO.setDeviceName(mysqlV2Model.getMysqlName());
        resourceDetailDTO.setTenantId(mysqlV2Model.getTenantId());
        resourceDetailDTO.setTenantName(orderDTO.getTenantName());
        resourceDetailDTO.setBusinessSysId(mysqlV2Model.getBusinessSystemId());
        resourceDetailDTO.setBusinessSysName(mysqlV2Model.getBusinessSystemName());
        resourceDetailDTO.setDomainCode(mysqlV2Model.getDomainCode());
        resourceDetailDTO.setDomainName(mysqlV2Model.getDomainName());
        resourceDetailDTO.setApplyUserName(orderDTO.getCreateByName());
        resourceDetailDTO.setResourcePoolId(String.valueOf(mysqlV2Model.getRegionId()));
        resourceDetailDTO.setResourcePoolCode(mysqlV2Model.getRegionCode());
        resourceDetailDTO.setResourcePoolName(mysqlV2Model.getRegionName());
        resourceDetailDTO.setCreateTime(LocalDateTime.now());
        resourceDetailDTO.setResourceApplyTime(orderDTO.getCreateTime());
        resourceDetailDTO.setEffectiveTime(LocalDateTime.now());
        resourceDetailDTO.setSourceExtType(CORPORATE.getPrefix());
        resourceDetailDTO.setAzId(ObjNullUtils.isNotNull(mysqlV2Model.getAzId()) ? mysqlV2Model.getAzId().toString() : null);
        resourceDetailDTO.setAzName(mysqlV2Model.getAzName());
        resourceDetailDTO.setAzCode(mysqlV2Model.getAzCode());
        resourceDetailDTO.setCloudPlatform(mysqlV2Model.getDomainCode());
        resourceDetailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), mysqlV2Model.getApplyTime()));
        resourceDetailDTO.setApplyTime(mysqlV2Model.getApplyTime());
        resourceDetailDTO.setApplyUserId(orderDTO.getCreateBy());
        resourceDetailDTO.setOrderType(CORPORATE.getCode());
        ;
        resourceDetailDTO.setDeviceStatus("ACTIVE");
        resourceDetailDTO.setSourceType(CORPORATE.getPrefix());
        resourceDetailDTO.setVpcId(Joiner.on(",")
                .skipNulls()
                .join(StreamUtils.mapArrayFilterNull(mysqlV2Model.getPlaneNetworkModel(), PlaneNetworkModel::getId)));
        resourceDetailDTO.setVpcName(Joiner.on(",")
                .skipNulls()
                .join(StreamUtils.mapArrayFilterNull(mysqlV2Model.getPlaneNetworkModel(), PlaneNetworkModel::getName)));
        resourceDetailDTO.setNetworkModelSnapshot(JSON.toJSONString(mysqlV2Model.getPlaneNetworkModel()));
        //子网可能出现一个以上的情况，用|分隔 每个网络模型的子网id用逗号分隔
        String subnetId = mysqlV2Model.getPlaneNetworkModel().stream()
                .map(networkModel ->
                        Joiner.on(",").skipNulls().join(
                                StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetId)
                        )
                )
                .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                .collect(Collectors.joining("|"));
        String subnetName = mysqlV2Model.getPlaneNetworkModel().stream()
                .map(networkModel ->
                        Joiner.on(",").skipNulls().join(
                                StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetName)
                        )
                )
                .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                .collect(Collectors.joining("|"));
        resourceDetailDTO.setSubnetId(subnetId);
        resourceDetailDTO.setSubnetName(subnetName);

        resourceDetailDTO.setOsVersion(mysqlV2Model.getEngineVersion());
        resourceDetailDTO.setSpec(String.format(mysqlV2Model.getFlavorName()));
        resourceDetailDTO.setSysDisk(mysqlV2Model.getSysDiskType() + " " + mysqlV2Model.getSysDiskSize() + "GB");
        //部署类型 存到 挂载云主机 字段中
        resourceDetailDTO.setMountOrNot(mysqlV2Model.getDeployType());
        resourceDetailManager.batchSaveResourceDetail(ListUtil.toList(resourceDetailDTO));
    }


    /**
     * 云主机回收成功后回调特殊处理
     *
     * @param productDTO
     */
    private void ecsComplete(DgRecoveryOrderProductDTO productDTO) {
        String resourceDetailId = productDTO.getResourceDetailId();
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
        //删除虚拟网卡
        VnicDTO vnicDTO = vnicManager.getByVmId(resourceDetailDTO.getDeviceId());
        if (Objects.nonNull(vnicDTO)) {
            vnicManager.delete(vnicDTO.getId());
        }
        //获取挂载的资源
        List<ResourceDetailDTO> mountList = resourceDetailManager
                .list(new ResourceDetailQuery().setVmId(resourceDetailDTO.getDeviceId())
                        .setTypeList(ListUtil.toList("eip", "evs")));
        //如果没有挂载的资源 就pass 如果有就把名字和id都改成null
        if (mountList.isEmpty()) {
            return;
        }
        //平台云特殊处理：平台云的云硬盘回收是跟着云主机的（不会下发云硬盘回收的子任务），在这里要把该云主机下的云硬盘都删掉
        if (DomainCodeEnum.PLF_PROC_NWC_ZJ_PLF_NEW.getCode().equals(resourceDetailDTO.getDomainCode())) {
            mountList.forEach(r -> resourceDetailManager.deleteById(r.getId()));
        }
        for (ResourceDetailDTO mountDTO : mountList) {
            ResourceDetailDTO updateDTO = new ResourceDetailDTO();
//            updateDTO.setVmId(null);
//            updateDTO.setEcsName(null);
            updateDTO.setId(mountDTO.getId());
            if (!productDTO.getSyncRecovery()) {
                updateDTO.setRecoveryStatus(RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
            }
            resourceDetailManager.clearRelatedInfoById(mountDTO.getId());
            resourceDetailManager.updateVmIdAndEscNameById(updateDTO);
        }
    }

    /**
     * 云硬盘回收成功后回调特特殊处理
     *
     * @param productDTO
     */
    private void evsComplete(DgRecoveryOrderProductDTO productDTO) {
        String resourceDetailId = productDTO.getResourceDetailId();
        ResourceDetailDTO evsDetail = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        Precondition.checkArgument(evsDetail, "找不到对应资源");
        String vmId = evsDetail.getVmId();
        if (StringUtils.isNotBlank(vmId)) {
            //如果是挂载了云主机，更新对应的云主机挂盘id和数据盘容量
            ResourceDetailDTO ecsDetail = resourceDetailManager.getByDeviceId(vmId);
            String volumeId = ecsDetail.getVolumeId();
            String dataDisk = ecsDetail.getDataDisk();
            if (StringUtils.isNotBlank(volumeId)) {
                List<String> list = Arrays.asList(volumeId.split(","));
                int index = list.indexOf(evsDetail.getDeviceId());
                List<String> dataDisks = new ArrayList<>(ListUtil.of(dataDisk.split(",")));
                List<String> volumeIds = new ArrayList<>(ListUtil.of(volumeId.split(",")));
                dataDisks.remove(index);
                volumeIds.remove(index);
                ecsDetail.setDataDisk(String.join(",", dataDisks));
                ecsDetail.setVolumeId(String.join(",", volumeIds));
                resourceDetailManager.updateById(ecsDetail);
            }
        }
    }

    /**
     * slb和net回收成功后回调特殊处理
     *
     * @param productDTO
     */
    private void slbAndNatComplete(DgRecoveryOrderProductDTO productDTO) {
        String resourceDetailId = productDTO.getResourceDetailId();
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
        String azId = resourceDetailDTO.getAzId();
        if (StringUtils.isNotBlank(azId)) {
            staticProductStockManager.increaseStockByAzAndType(azId, productDTO.getProductType());
        }
        if (resourceDetailDTO.getEipId() == null) {
            return;
        }
        ResourceDetailDTO resourceDetailEipDTO = resourceDetailManager.getByDeviceId(resourceDetailDTO.getEipId());
        if (resourceDetailEipDTO == null) {
            log.warn("找不到对应eip资源, resource id:{}, eipId:{}", resourceDetailId, resourceDetailDTO.getEipId());
            return;
        }
        if (!productDTO.getSyncRecovery()) {
            resourceDetailEipDTO.setRecoveryStatus(RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
            resourceDetailManager.clearRelatedInfoById(resourceDetailEipDTO.getId());
            resourceDetailManager.updateEipById(resourceDetailEipDTO);
        } else {
            resourceDetailDTO.setRecoveryStatus(RecoveryStatusEnum.RECOVERY_COMPLETE.getType());
            resourceDetailManager.deleteById(resourceDetailEipDTO.getId());
        }
    }

    /**
     * eip回收成功后回调特殊处理
     *
     * @param productDTO
     */
    private void eipComplete(DgRecoveryOrderProductDTO productDTO) {
        String resourceDetailId = productDTO.getResourceDetailId();
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
        resourceDetailManager.clearEipInfoByEipId(resourceDetailDTO.getDeviceId());
    }
} 