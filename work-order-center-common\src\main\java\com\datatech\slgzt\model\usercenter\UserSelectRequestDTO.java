package com.datatech.slgzt.model.usercenter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 封装用户体系请求参数
 *
 * @Author: l<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/1/8
 */

@Data
@Accessors(chain = true)
public class UserSelectRequestDTO implements Serializable {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户登录名称
     */
    private String account;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     * 每页显示条数
     */
    private Integer pageSize;

}

