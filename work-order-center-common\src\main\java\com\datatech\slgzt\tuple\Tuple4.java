package com.datatech.slgzt.tuple;

import java.io.Serializable;

public class Tuple4<A, B, C, D> implements Serializable {
    public final A a;
    public final B b;
    public final C c;
    public final D d;

    public Tuple4(A a, B b, C c, D d) {
        this.a = a;
        this.b = b;
        this.c = c;
        this.d = d;
    }

    public static <A, B, C, D> Tuple4<A, B, C, D> of(A a, B b, C c, D d) {
        return new Tuple4<>(a, b, c, d);
    }

    @Override
    public String toString() {
        return "(" + a + ", " + b + ", " + c + ", " + d + ")";
    }
} 