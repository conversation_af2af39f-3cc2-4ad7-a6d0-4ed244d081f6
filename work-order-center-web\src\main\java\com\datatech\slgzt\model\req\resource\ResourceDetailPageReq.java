package com.datatech.slgzt.model.req.resource;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月15日 19:28:14
 */
@Data
public class ResourceDetailPageReq {

    /**
     * 云资源统一模型类（截至2025年3月）
     * 包含主流云服务资源字段及业务属性
     */
    private Integer pageSize;

    private Integer pageNum;

    private String orderId;

    private String deviceId;

    private String type;

    private List<String> typeList;

    private String vpcId;

    // 挂载云主机时列表中支持keywords多条件查询
    private String keywords;
    //回收状态列表
    private List<Integer> recoveryStatusList;

    //退订状态
    private String disOrderStatus;

    // 通用字段（所有服务共有）
    private String applyTime;         // 资源名称（ECS/OBS/SLB/NAT网关等设备名称）
    private String deviceName;         // 资源名称（ECS/OBS/SLB/NAT网关等设备名称）
    private String resourceId;         // 资源唯一标识（ECS/GCS特有）
    private String tenantName;          // 租户信息（所属组织/部门）
    private String businessSysName;     // 关联业务系统（如财务系统/CRM系统）
    private String businessSysId;
    private String cloudPlatform;       // 所属云平台（如阿里云/华为云）
    private String resourcePoolName;    // 资源池（集群/区域划分）
    private String resourcePoolId;    // 资源池（集群/区域划分）
    private String orderCode;           // 工单编号（资源申请流程标识）
    private LocalDateTime createTimeStart;            // 创建时间（资源开通时间）
    private LocalDateTime createTimeEnd;            // 创建时间（资源开通时间）
    private LocalDateTime expireTimeStart;            // 到期时间（计费周期截止）
    private LocalDateTime expireTimeEnd;            // 到期时间（计费周期截止）
    private String billId;              // 计费账号（财务结算标识）
    private String deviceStatus;        // 资源状态（运行中/已停止/异常）
    private String applyUserName;       // 申请人（资源申请责任人）

    // 计算资源（ECS/GCS云主机）
    private String osVersion;           // 操作系统版本（如CentOS 7.6）
    private String spec;                // 实例规格（vCPU/内存配置，如2C4G）
    private String sysDisk;             // 系统盘配置（类型+容量，如SSD 100GB）
    private String dataDisk;            // 数据盘配置（类型+容量，如SAS 500GB）
    private String ip;                  // 内网IP地址（私有网络通信地址）
    private String eip;                 // 弹性公网IP（互联网访问地址）
    private String bandWidth;           // 网络带宽（如100Mbps）
    private String instanceUuid;        // 实例唯一标识（虚拟机全局ID）

    // 存储资源（EVS/OBS）
    private String storeType;           // 存储类型（OBS：标准/低频/归档；EVS：SSD/SAS）
    private String capacity;            // 存储容量（如OBS 5TB/EVS 1TB）
    private String accessKey;           // 公钥（对象存储访问凭证）
    private String secretKey;           // 私钥（对象存储安全密钥）
    private String publicAddress;       // 公网访问端点（如obs.example.com）
    private String internalAddress;     // 内网访问端点（如obs-internal.example.com）
    private String mountOrNot;         // 挂载状态（EVS是否挂载到ECS）
    private String ecsName;             // 关联云主机（EVS挂载的ECS名称）

    // 网络资源（SLB/NAT网关）
    private String vpcName;             // 虚拟私有云（所属VPC名称）
    private String subnetName;          // 子网信息（所属子网划分）
    private LocalDateTime effectiveTimeStart;         // 生效时间（SLB/NAT配置生效时间）
    private LocalDateTime effectiveTimeEnd;         // 生效时间（SLB/NAT配置生效时间）

    private String handoverStatus;
    // 构造方法、getter/setter省略（按需补充）

    /**
     * 来源
     */
    private String sourceType;

    /**
     * 云主机id，用于与云主机存在绑定关系的资源查询
     */
    private String vmId;

    /**
     * 备份策略id
     */
    private String backupId;

    /**
     * 备份策略类型
     */
    private String backupType;

    private String customId;

}
