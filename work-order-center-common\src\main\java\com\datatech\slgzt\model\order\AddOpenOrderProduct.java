package com.datatech.slgzt.model.order;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 开通资源产品订单-DTO类
 * <AUTHOR>
 * @Date: 2024/11/18 15:49
 * @Description:
 */
@Data
public class AddOpenOrderProduct implements Serializable {

    /**
     * 产品编号
     */
    private Long productId;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String productName;

    /**
     * 产品类型
     */
    @NotBlank(message = "产品类型不能为空")
    private String productType;

    /**
     * 服务组集合
     */
//    @Valid
//    private List<AddOpenOrderServiceGroupDTO> serviceGroups;

    /**
     * 产品属性集合
     */
//    @Valid
//    private List<ProductOrderAttrDTO> attrs;
}
