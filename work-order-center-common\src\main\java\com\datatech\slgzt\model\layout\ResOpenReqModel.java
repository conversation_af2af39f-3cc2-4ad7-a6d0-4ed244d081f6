package com.datatech.slgzt.model.layout;

import com.datatech.slgzt.model.nostander.PNatModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月07日 18:39:40
 */
@Data
public class ResOpenReqModel {

    /**
     * 计费号
     */
    private String account;


    private String SourceExtType;


    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * taskSource
     */
    private Integer taskSource;

    /**
     * userId
     * 拿当前登录用户id
     */
    private Long userId;

    /**
     * 业务code
     * 固定
     */
    private String businessCode ;


    private String businessSystemCode;

    /**
     * 客户id
     */
    private String customId;

    /**
     * "region.edge_prov_zj_hws.nb",// 通过云类型去 资源池表里查 epc
     */
    private String regionCode;

    /**
     * subOrderId
     * 填写非标工单id
     */
    private Long subOrderId;


    private String gid;


    private List<ProductOrder> productOrders;

    @Data
    public static class ProductOrder {
        private Attrs attrs; // 订单属性
        private String gId; // 全局 ID
//        private List<String> operations; // 操作列表
        private String outInstanceId; // 外部实例 ID
        private String productOrderId; // 订单 ID
        private String productOrderType; // 订单类型（固定值）
        private String productType; // 产品类型（固定值）
        private String subOrderId; // 子订单 ID
        //操作列表  如EVS_MOUNT
        private List<Operations> operations;

    }

    @Data
    public static class Attrs {
        private String businessSystemCode;
        private String rdsName; // RDS 名称
        private String billId;
        private String natName;
        private String sourceOrderCode;
        private String optUuid;
        private String systemSource;
        private String groupId;
        private String slbName;
        private String name;
        private String addressType;
        private String vpcId; // VPC ID
        private String subnetId; // 子网 ID
        private String plane; // 私网地址
        private String imageVersion; // 系统版本
        private String vmName; // 主机名称
        private String functionalModule; // 功能模块
        private String gId; // 全局 ID
        private String imageId; // 镜像 ID
        private PNatModel vpcInfo; // VPC 信息
        private String flavorCode; // 规格
        private String flavorId; // 规格
        private String outInstanceId; // 外部实例 ID
        private String imageOs; // 系统名称
        private String userName; // 云主机用户名称
        private String adminPass; // 密码
        private RootVolume rootVolume; // 系统盘
        private String azCode; // 可用区代码
        private String time; // 时间

        // 数据盘相关字段
        private String volumeType; // 磁盘类型
        private String regionCode; // 区域代码
        private String volumeName; // 磁盘名称
        private String evsName; // EVS 名称
        private Long tenantId; // 租户 ID
        private Integer volumeSize; // 磁盘大小
        private String vmId;
        private String id;

        // 弹性公网 IP 相关字段
        private Integer bandwidth; // 带宽
        private String ecsResourceId; // 挂载云主机的id

//        private String deviceId;
//
//        private String deviceType;
        private String eipName; // 名称

        private String ip;

        //----obs字段-----
        //扩容
        //配额大小
        private Integer quota;
        //obs实例id
        //private String gId;
        //创建
        //todo 默认创建桶：0-否，1-是
        private Integer createDefaultBucket = 1;
        //桶信息
        private ObsBucket createBucket;

        //云备份策略字段
        //备份类型 ECS/EVS
        private String backupType;
        //策略名称
        private String jobName;
        //备份频率 weeks/days， 若type=“days”时填写，表示每天都备份执行
        private String frequency;
        //若type=“weeks”时填写：每周几执行，1-7分别代表周一到周日
        private Integer daysOfWeek;
        //需要备份的云硬盘/云硬盘id
        private List<String> objectIdList;

        //vpn
        //允许同时连接的最大客户端数量
        private Integer maxConnection;

        //nas
        //路径
        private String path;
        //存储大小
        private Long storageSize;

        private String rdsLoginName;
        private String rdsPwd;

        //rds相关字段
        private String engineVersion; // 引擎版本

        private String deployType; // 部署类型

        private String timeZone; // 时区

        private Integer tableIsLower; // 区分大小写
        private Integer dbEngine; // 区分大小写

        private String storageType; // 系统盘类型


    }



    @Data
    @Accessors(chain = true)
    public static class RootVolume {
        private String sysDiskType; // 系统盘类型
        private int sysDiskSize; // 系统盘大小
    }

    @Data
    public static class ObsBucket {
        private String bucketName;
        private Integer bucketSize;
        private Integer writeProtect = 0;
        private Integer multiVersion = 0;
        private String storageType = "STANDARD";
        private String strategy = "PRIVICE";
    }


    @Data
    public static class Operations {
        /**
         * 操作类型
         */
        private String operation;
        /**
         * 操作id
         */
        private List<OperationData> OperationDataList;


    }

    @Data
    public static class OperationData {
        /**
         * 操作的id
         */
        private String operationId;
        /**
         * 操作的状态 暂位
         */
        private String operationStatus;
    }

    @Data
    public static class Nic {
//        // 子网id
//        private String subnetId;

        // 待创建云服务器网卡的IP地址
        private String ipAddress;

    }

}
