package com.datatech.slgzt.utils;

import cn.hutool.core.bean.BeanUtil;
import com.datatech.slgzt.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.*;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Http-工具类
 *
 * <AUTHOR>
 * @Data 2024-11-18 16:58
 * @Description HttpClientUtil
 */
public class HttpClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);

    public static String get(String url) {
        HttpGet request = new HttpGet(url);
        CloseableHttpResponse response = get(request);
        return printEntity(response.getEntity());
    }

    public static String getHaveHeader(String url, Map<String, String> map) {
        HttpGet request = new HttpGet(url);
        if (map != null && !map.isEmpty()) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                request.addHeader(entry.getKey(), entry.getValue().toString());
            }
        }
        CloseableHttpResponse response = get(request);
        return printEntity(response.getEntity());
    }

    public static CloseableHttpResponse get(HttpGet request) {
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = getHttpClient();
        try {
            response = httpClient.execute(request);
        } catch (Exception e) {
            logger.error("异常:{}", e);
            throw new RuntimeException(e);
        }
        return response;
    }

    public static String getAppendUrl(String url, Map<String, Object> map) {
        if (map != null && !map.isEmpty()) {
            StringBuffer buffer = new StringBuffer();
            Iterator<Map.Entry<String, Object>> iterator = map.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Object> entry = iterator.next();
                if (StringUtils.isEmpty(buffer.toString())) {
                    buffer.append("?");
                } else {
                    buffer.append("&");
                }
                buffer.append(entry.getKey()).append("=").append(entry.getValue());
            }
            url += buffer.toString();
        }
        return url;
    }

    public static Map<String, Object> get(String url, Object param, Map<String, String> header) {
        String appendUrl = "";
        if (param instanceof Map) {
            appendUrl = getAppendUrl(url, (Map<String, Object>) param);
        } else {

            appendUrl = getAppendUrl(url, BeanUtil.beanToMap(param));
        }
        HttpGet request = new HttpGet(appendUrl);
        if (header != null && !header.isEmpty()) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                request.addHeader(entry.getKey(), entry.getValue().toString());
            }
        }
        CloseableHttpResponse response = get(request);
        int i = response.getStatusLine().getStatusCode();
        Map<String, Object> getMap = new HashMap<String, Object>();

        getMap.put("code", i);
        getMap.put("message", "");
        getMap.put("json", printEntity(response.getEntity()));
        try {
            response.close();
        } catch (IOException e) {
            logger.error("IOException: {}", e);
        }
        return getMap;
    }

    public static Map<String, Object> post(String url, String param, Map<String, String> map) {
        Map<String, Object> postmap = new HashMap<String, Object>();
        HttpPost request = new HttpPost(url);
        if (map != null && !map.isEmpty()) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                request.addHeader(entry.getKey(), entry.getValue().toString());
            }
        }
        request.setEntity(new StringEntity(param, "utf-8"));
        CloseableHttpResponse response = post(request);
        int i = response.getStatusLine().getStatusCode();
        postmap.put("code", i);
        postmap.put("json", printEntity(response.getEntity()));
        try {
            response.close();
        } catch (IOException e) {
            logger.error("IOException: {}", e);
        }
        return postmap;
    }

    public static CloseableHttpResponse post(HttpPost request) {
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = getHttpClient();
        try {
            response = httpClient.execute(request);
        } catch (Exception e) {
            logger.error("异常:{}", e);
            throw new RuntimeException(e);
        }
        return response;
    }

    public static CloseableHttpClient getHttpClient() {
        RegistryBuilder<ConnectionSocketFactory> registryBuilder = RegistryBuilder.<ConnectionSocketFactory>create();
        ConnectionSocketFactory plainSF = new PlainConnectionSocketFactory();
        registryBuilder.register("http", plainSF);
        // 指定信任密钥存储对象和连接套接字工厂
        try {
            KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
            // 信任任何链接
            TrustStrategy anyTrustStrategy = new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                    return true;
                }
            };
            SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(trustStore, anyTrustStrategy).build();
            LayeredConnectionSocketFactory sslSF = new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
            registryBuilder.register("https", sslSF);
        } catch (KeyStoreException e) {
            throw new RuntimeException(e);
        } catch (KeyManagementException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        Registry<ConnectionSocketFactory> registry = registryBuilder.build();
        // 设置连接管理器
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(registry);

        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(3 * 60 * 1000)
                .setConnectTimeout(3 * 60 * 1000).setConnectionRequestTimeout(3 * 60 * 1000)
                .build();

        // 构建客户端
        return HttpClientBuilder.create().setDefaultRequestConfig(defaultRequestConfig).setConnectionManager(connManager).build();
    }

    private static String printEntity(HttpEntity entity) {
        if (entity != null) {
            InputStream instreams = null;
            try {
                instreams = entity.getContent();
            } catch (IOException e) {
                logger.error("IOException: {}", e);
                return null;
            }
            BufferedReader reader = null;
            try {
                reader = new BufferedReader(new InputStreamReader(instreams, "utf-8"));
            } catch (UnsupportedEncodingException e1) {
                logger.error("UnsupportedEncodingException: {}", e1);
                return null;
            }
            StringBuilder sb = new StringBuilder();
            String line = null;
            try {
                while ((line = reader.readLine()) != null) {
                    sb.append(line).append("\n");
                }
            } catch (IOException e) {
                logger.error("IOException:{}", e);
            } finally {
                try {
                    reader.close();
                    instreams.close();
                } catch (IOException e) {
                    logger.error("IOException: {}", e);
                }
            }
            return sb.toString();
        }
        return null;
    }

    /**
     * 发送post请求，携带json类型数据
     * 如：{"name":"jok","age":"10"}
     *
     * @param url  请求地址
     * @param json json格式参数
     * @return
     */
    public static String doPostJson(String url, String json, Map<String, String> map) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);
            if (map != null && !map.isEmpty()) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }
            // 创建请求内容
            StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            // 执行http请求
            response = httpClient.execute(httpPost);
            int i = response.getStatusLine().getStatusCode();
            if (i != HttpStatus.SC_OK) {
                throw new BusinessException("请求失败状态码为:" + i);
            }

            resultString = EntityUtils.toString(response.getEntity(), getDefaultCharSet());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return resultString;
    }

    /**
     * 设置编码格式utf-8,防止乱码
     *
     * @return utf-8
     */
    private static String getDefaultCharSet() {
        OutputStreamWriter writer = new OutputStreamWriter(new ByteArrayOutputStream());
        return writer.getEncoding();
    }
}