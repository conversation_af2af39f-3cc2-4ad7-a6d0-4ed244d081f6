package com.datatech.slgzt.utils;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Java8使用stream操作两个list根据某字段匹配再对其中一个list进行赋值
 */
@Slf4j
public class ListUtils {

    /**
     * lambda表达式对两个List进行循环,根据符合条件,进行相关的赋值操作并返回这个对象集合
     *
     * @param sourceList   待设置源列表
     * @param srcEqualProp 源对象条件判断属性名
     * @param srcSetProp   源对象待设置属性名
     * @param targetList   资源提供者列表
     * @param tarEqualProp 对象条件判断参数名
     * @param tarGetProp   待获取对象属性名
     * @param <T>
     * @param <U>
     * @return
     */
    public static <T, U> List<T> setListByEqualObjProperty(List<T> sourceList, String srcEqualProp, String srcSetProp,
                                                           List<U> targetList, String tarEqualProp, String tarGetProp) {
        List<T> resultList = Lists.newArrayList();
        resultList = sourceList.stream()
                .map(sur -> targetList.stream()
                        .filter(tar -> Objects.equals(getValueByPropName(sur, srcEqualProp), getValueByPropName(tar, tarEqualProp)))
                        .findFirst()
                        .map(tar -> {
                            setValueByPropName(sur, srcSetProp, getValueByPropName(tar, tarGetProp));
                            return sur;
                        }).orElse(null))
                .collect(Collectors.toList());

        return resultList;
    }

    /**
     * 通过遍历两个List中按id属性相等的归结到resultList中
     *
     * @param oneList      源list 1
     * @param twoList      源list 2
     * @param equalKeyName 相等的map键值
     */
    public static List<Map<Object, Object>> compareListHitData(List<Map<Object, Object>> oneList, List<Map<Object, Object>> twoList, Object equalKeyName) {
        List<Map<Object, Object>> resultList = oneList.stream().map(map -> twoList.stream()
                        .filter(m -> Objects.equals(m.get(equalKeyName), map.get(equalKeyName)))
                        .findFirst().map(m -> {
                            map.putAll(m);
                            return map;
                        }).orElse(null))
                .filter(Objects::nonNull).collect(Collectors.toList());
        return resultList;
    }

    // 通过属性获取传入对象的指定属性的值
    public static <T> T getValueByPropName(Object object, String propName) {
        T value = null;
        try {
            // 通过属性获取对象的属性
            Field field = object.getClass().getDeclaredField(propName);
            // 对象的属性的访问权限设置为可访问
            field.setAccessible(true);
            // 获取属性的对应的值
            value = (T) field.get(object);
        } catch (Exception e) {
            return null;
        }

        return value;
    }

    // 通过属性设置传入对象的指定属性的值
    public static <U> void setValueByPropName(Object object, String propName, U updateValue) {

        try {
            // 通过属性获取对象的属性
            Field field = object.getClass().getDeclaredField(propName);
            // 对象的属性的访问权限设置为可访问
            field.setAccessible(true);
            // 设置属性的对应的值
            field.set(object, updateValue);
        } catch (Exception e) {
            log.error("setValueByPropName.error {}", propName, e);
        }

    }

    /**
     * 获取集合中的第一个元素
     *
     * @param collection 集合
     * @param clazz      返回对象类型
     * @param <T>
     * @return
     */
    public static <T> T getFirstElent(Collection<?> collection, Class<T> clazz) {
        // 确保传入的集合不为空
        if (collection == null || collection.isEmpty()) {
            return null;
        }

        // 通过反射获取集合的 iterator 方法
        try {
            Method iteratorMethod = collection.getClass().getMethod("iterator");
            Object iterator = iteratorMethod.invoke(collection);

            // 获取 iterator 的 next 方法
            Method nextMethod = iterator.getClass().getMethod("next");
            nextMethod.setAccessible(true);

            // 获取第一个元素
            Object firstElement = nextMethod.invoke(iterator);
            // 如果传入的类型与第一个元素的类型匹配，则返回该元素
            if (clazz.isInstance(firstElement)) {
                return clazz.cast(firstElement); // 强制类型转换为传入的类型
            }
        } catch (Exception e) {
            log.error("获取集合第一个元素失败", e.fillInStackTrace());
            return null;
        }

        return null;
    }

    /**
     * Java对象转map，支持传入过滤字段名进行过滤返回数据
     *
     * @param obj          待转对象
     * @param filterFields 过滤条件
     * @return map
     */
    public static Map<String, Object> toMap(Object obj, String... filterFields) {
        Map<String, Object> result = new HashMap<>();
        if (obj == null) {
            return result;
        }

        Class<?> clazz = obj.getClass();
        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                try {
                    Object value = field.get(obj);
                    if (value != null) {
                        if (field.getName().equalsIgnoreCase("serialVersionUID")) {
                            continue;
                        }

                        if (filterFields.length > 0) {
                            boolean match = Arrays.stream(filterFields).anyMatch(item -> item.equals(field.getName()));
                            if (match) {
                                result.put(field.getName(), value);
                            }
                        } else {
                            result.put(field.getName(), value);
                        }
                    }
                } catch (IllegalAccessException e) {
                    log.error("对象转map时失败:message:{}", e.getMessage());
                    e.printStackTrace();
                }
            }

            // 处理父类字段
            clazz = clazz.getSuperclass();
        }
        return result;
    }
}
