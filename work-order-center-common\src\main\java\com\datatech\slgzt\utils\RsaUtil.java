package com.datatech.slgzt.utils;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 10:20:03
 */
public class RsaUtil {

    public static String encrypt(String data, String publicKeyStr) {
        try {
            // Base64解码公钥
            byte[] decodedKey = Base64.getDecoder().decode(publicKeyStr);
            // 生成公钥
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodedKey);
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            // 使用公钥加密数据
            byte[] encryptedData = encrypt(data, publicKey);
            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }


    public static byte[] encrypt(String plaintext, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(plaintext.getBytes());
    }



}
