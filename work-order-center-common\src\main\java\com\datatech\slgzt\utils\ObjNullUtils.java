package com.datatech.slgzt.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

/**
 * 对象判空工具类
 * 更具不同的对象类型，判断是否为空
 */
public class ObjNullUtils {


    public static boolean isNotNull(Object obj) {
        if (obj instanceof String) {
            return StringUtils.isNotBlank((String) obj);
        }
        if (obj instanceof Collection) {
            Collection list = (Collection) obj;
            if (list == null) {
                return false;
            }
            return !list.isEmpty();
        }
        if (obj != null) {
            return true;
        }
        return false;
    }

    public static boolean isNull(Object obj) {
        return !isNotNull(obj);
    }
}
