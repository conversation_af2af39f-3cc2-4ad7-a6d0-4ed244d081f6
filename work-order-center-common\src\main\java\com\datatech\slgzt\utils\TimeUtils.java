package com.datatech.slgzt.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Date;

/**
 * 校验时间工具
 *
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2024/11/29
 */
public class TimeUtils {

    private static Logger logger = LoggerFactory.getLogger(TimeUtils.class);

    private static DateTimeFormatter Y_M_D_H_M_S = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static DateTimeFormatter Y_M_D = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static LocalDate stringToLocalDate(String dateStr) {
        return LocalDate.parse(dateStr, Y_M_D);
    }

    public static Date stringToDate(String dateStr) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateStr, Y_M_D_H_M_S);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 日期校验
     *
     * @param date 日期
     * @return true 满足 false 不满足
     */
    public static boolean checkTimeFormat(String date) {
        try {
            LocalDateTime.parse(date, Y_M_D_H_M_S);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 判断传入的月份是否为当前月
     *
     * @param inputMonth 月份
     * @return true:当前月 false:上个月
     */
    public static boolean checkMonth(int inputMonth) {
        LocalDate currentDate = LocalDate.now();
        int currentMonth = currentDate.getMonthValue();
        return inputMonth == currentMonth;
    }
}

