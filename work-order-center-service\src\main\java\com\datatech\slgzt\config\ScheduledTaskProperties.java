package com.datatech.slgzt.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 定时任务配置
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "schedule")
public class ScheduledTaskProperties {

    // 默认每天凌晨0点执行一次
    private String dgRecoveryCron = "0 0 0 * * ?";

    // 默认1小时一次
    private String dgShutdownCron = "0 0 0/1 * * ?";
}
