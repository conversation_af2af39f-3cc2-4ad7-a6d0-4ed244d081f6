package com.datatech.slgzt.utils;

import com.ejlchina.okhttps.HTTP;
import com.ejlchina.okhttps.fastjson.FastjsonMsgConvertor;
import okhttp3.OkHttpClient;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.concurrent.TimeUnit;

public class OkHttpsUtils {

    // 创建自定义信任管理器
    private static TrustManager[] trustAllCerts = new TrustManager[]{
            new X509TrustManager() {
                public void checkClientTrusted(X509Certificate[] chain, String authType) {
                }

                public void checkServerTrusted(X509Certificate[] chain, String authType) {
                }

                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            }
    };


    /**
     * 构建一个超时60的OkHttps对象
     */
    public static HTTP http() {

        return HTTP.builder()
                .config((OkHttpClient.Builder builder) -> {
                    // 连接超时时间（默认10秒）
                    builder.connectTimeout(120, TimeUnit.SECONDS);
                    // 写入超时时间（默认10秒）
                    builder.writeTimeout(120, TimeUnit.SECONDS);
                    // 读取超时时间（默认10秒）
                    builder.readTimeout(120, TimeUnit.SECONDS);
                })
                // 添加默认的JSON转换器
                .addMsgConvertor(new FastjsonMsgConvertor())
                .build();
    }

    /**
     * 构建一个忽略未信任的https证书的http请求
     */
    public static HTTP httpIgnoreHttpsCertificate() throws NoSuchAlgorithmException, KeyManagementException {
        return httpIgnoreHttpsCertificate(null);
    }

    /**
     * 构建一个忽略未信任的https证书的http请求
     */
    public static HTTP httpIgnoreHttpsCertificate(Integer timeoutSeconds) throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustAllCerts, new SecureRandom());
        return HTTP.builder()
                .config(builder -> {
                    builder.hostnameVerifier((host, session) -> true)
                            .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0]);
                    if (timeoutSeconds != null) {
                        builder.connectTimeout(timeoutSeconds, TimeUnit.SECONDS);
                        builder.writeTimeout(timeoutSeconds, TimeUnit.SECONDS);
                        builder.readTimeout(timeoutSeconds, TimeUnit.SECONDS);
                    }
                })
                // 添加默认的JSON转换器
                .addMsgConvertor(new FastjsonMsgConvertor())
                .build();
    }

}