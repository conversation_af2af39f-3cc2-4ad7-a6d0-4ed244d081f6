package com.datatech.slgzt.utils;

import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Method;
import java.util.Optional;

/**
 * spring el表达式解析
 */
public class SpElUtils {

    // 表达式解析器
    private static final ExpressionParser parser = new SpelExpressionParser();

    // 方法参数名称解析
    private static final DefaultParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    /**
     * @param method 目标方法
     * @param args 目标方法的参数值列表
     * @param spEl spEl表达式
     */
    public static String parseSpEl(Method method, Object[] args, String spEl) {

        // 解析参数名称列表
        String[] params = Optional.ofNullable(parameterNameDiscoverer.getParameterNames(method)).orElse(new String[]{});

        // 自定义解析器需要的上下文对象,不使用默认的spring容器上下文对象
        EvaluationContext context = new StandardEvaluationContext();

        // 上下文中设置所有参数名称以及参数值
        for (int i = 0; i < params.length; i++) {
            context.setVariable(params[i], args[i]);
        }
        // 获取表达式
        Expression expression = parser.parseExpression(spEl);

        // 解析
        // spel表达式示例：#name.value, 则从上下文中获取name对象的value
        return expression.getValue(context, String.class);
    }

    public static String getMethodKey(Method method) {
        // 获取方法的全限定名
        return method.getDeclaringClass() + "#" + method.getName();
    }
}
