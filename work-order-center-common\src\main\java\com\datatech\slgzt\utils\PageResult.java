package com.datatech.slgzt.utils;

import lombok.Data;

import java.util.List;

/**
 * 统一分页
 *
 * <AUTHOR>
 */
@Data
public class PageResult<T> {
    private List<T> records;
    private long pageSize;
    private long pageNum;
    private long total;
    private long pages;

    /**
     * 统一分页构造器
     *
     * @param records
     * @param pageNum
     * @param pageSize
     * @param total
     */
    public PageResult(List<T> records, long pageNum, long pageSize, long total) {
        this.records = records;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.pages = this.getPages();
    }

    public PageResult(long pageNum, long pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public PageResult() {

    }

    public long getPages() {
        if (getPageSize() == 0) {
            return 0L;
        }
        long pages = getTotal() / getPageSize();
        if (getTotal() % getPageSize() != 0) {
            pages++;
        }
        return pages;
    }

}