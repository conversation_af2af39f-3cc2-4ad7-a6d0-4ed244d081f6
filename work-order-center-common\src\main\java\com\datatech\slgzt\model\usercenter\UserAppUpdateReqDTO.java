package com.datatech.slgzt.model.usercenter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 封装用户体系请求参数
 *
 * @Author: liu<PERSON><PERSON>
 * @Date: 2025/4/16
 */

@Data
public class UserAppUpdateReqDTO {

    /**
     * 业务系统id
     */
    private Long appId;

    /**
     * 业务模块id
     */
    private Long appModuleId;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 云平台
     */
    private String domainCode;

    /**
     * 资源池编码
     */
    private String regionCode;

    /**
     * 生命周期状态，1-工程 2-在网 3-下线
     */
    private Integer lifeCycle;

}

