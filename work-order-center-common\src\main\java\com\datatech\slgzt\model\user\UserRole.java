package com.datatech.slgzt.model.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色表 --实体类
 *
 * <AUTHOR>
 * @date 2024/11/18 16:42
 **/
@Data
public class UserRole implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    private Long updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;

    private Integer status;

    /**
     * 角色名称
     */
    private String name;

    /**
     * 角色编码
     */
    private String code;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 角色作用域
     */
    private String roleScope;

    /**
     * 角色作用对象编号
     */
    private Long roleEntityId;

    /**
     * 云平台
     */
    private String domainCode;


    /**
     * 排序
     */
    private Integer sort;

    private String authMenuCode;


}